/*
Theme Name:  List<PERSON> Child Theme
Theme URI: http://listeo.pro
Author: Purethemes
Author URI: http://themeforest.net/user/purethemes
Description: Directory WordPress Theme by Purethemes
Version: 1.1.4
License: ThemeForest
License URI: http://themeforest.net/licenses
Text Domain: listeo
Domain Path: /languages/
Tags: light, responsive-layout, post-formats, theme-options, translation-ready, two-columns
Template: listeo
*/

.listing-item-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 5px;
	margin-bottom: 12px;
	margin-right: -60px;
}

.listing-item-tags span.tag {
	margin: 0 !important;
	font-size: 11px !important;
	position: static !important;
	background: #4dd6b8;
	font-weight: 400;
	border-radius: 50px;
	padding: 5px 12px;
	line-height: 15px;
	text-transform: none;
}

.listing-ndis-container {
	position: absolute;
	top: 25px;
	right: 25px;
	z-index: 110;
}

.listing-ndis-container img {
	width: 67px !important;
}

.fs-inner-container.content.faded-out .listings-container {
	opacity: 1;
}

#widget_contact_widget_listeo-5.boxed-widget.message-vendor {
	margin-bottom: 0;
}

.leaflet-listing-img-container img {
	object-fit: contain;
	background: #fff;
}

#header {
	background: #fff;
}

@media (min-width: 1025px) {
	body.full-width-header .right-side {
		width: auto;
	}

	body.full-width-header .left-side {
		width: auto;
	}

	body.full-width-header #header .container {
		display: flex;
		justify-content: space-between;
	}

	#header-container .header-search-container {
		flex: 1;
		opacity: 1;
		visibility: visible;
		display: flex;
		align-items: center;
	}

	#header-container .header-search-container .main-search-form {
		max-width: 800px;
		margin: 0 auto;
		flex: 1;
	}

	body.full-width-header #header .container::before,
	body.full-width-header #header .container::after {
		display: none;
	}
}

.custom_listing_field.is_not_in svg circle {
	fill: rgba(248, 0, 68, 0.11);
}

.custom_listing_field.is_not_in svg path {
	stroke: #f91942;
}

.pricing-package-details .plan-features-auto-wc {
	display: none;
}

.pricing-package-details ul li:not(.custom_listing_field) {
	display: none;
}

.user_not_logged_in .header-widget .button:not(.wc-forward) {
	display: inline-block;
}

.leaflet-listing-item-content {
	position: static;
	padding-top: 15px;
}

.leaflet-listing-img-container {
	height: auto;
}

.leaflet-listing-img-container::before {
	display: none;
}

.leaflet-listing-item-content h3 {
	font-size: 17px;
	line-height: 1.3;
	color: #000;
	margin-bottom: 10px;
}

.leaflet-listing-item-content span {
	font-size: 14px;
	line-height: 1.4;
	color: #000;
}

.leaflet-listing-content {
	padding-top: 10px;
}

.leaflet-listing-img-container:hover {
	text-decoration: none;
}

.leaflet-listing-img-container img {
	aspect-ratio: 1 / 1;
}

@media (max-width: 1024px) {
	#header-container .header-search-container {
		display: none;
	}
}

@media (max-width: 768px) {
	.list-layout .listing-ndis-container {
		top: -280px;
	}
}

@media (max-width: 480px) {
	.list-layout .listing-ndis-container {
		top: -180px;
	}
}

.elementor-widget-listeo-listings .listing-small-badges-container,
.elementor-widget-listeo-listings .listing-ndis-container,
.elementor-widget-listeo-listings-carousel .listing-small-badges-container,
.elementor-widget-listeo-listings-carousel .listing-ndis-container {
	display: none;
}

.elementor-widget-listeo-listings .listing-item-container,
.elementor-widget-listeo-listings-carousel .listing-item-container {
	background: #F9F9F9;
	display: block;
	max-height: none !important;
	margin-bottom: 0;
}

.elementor-widget-listeo-listings .listing-item-content,
.elementor-widget-listeo-listings-carousel .listing-item-content {
	position: static;
	padding-top: 15px;
	padding-left: 15px;
	padding-right: 15px;
}

.elementor-widget-listeo-listings .listing-item-container .star-rating,
.elementor-widget-listeo-listings-carousel .listing-item-container .star-rating {
	padding-left: 12px;
	padding-right: 12px;
	font-size: 15px;
}

.save.like-icon {
	display: none;
}

.elementor-widget-listeo-listings .listing-item,
.elementor-widget-listeo-listings-carousel .listing-item {
	height: auto;
	background: none;
	background: #F9F9F9;
}

.elementor-widget-listeo-listings .listing-item::before,
.elementor-widget-listeo-listings-carousel .listing-item::before {
	display: none;
}

.elementor-widget-listeo-listings .listing-item-content h3,
.elementor-widget-listeo-listings .listing-item-content span,
.elementor-widget-listeo-listings-carousel .listing-item-content h3,
.elementor-widget-listeo-listings-carousel .listing-item-content span {
	color: #000;
}

.elementor-widget-listeo-listings .listing-item-content h3,
.elementor-widget-listeo-listings-carousel .listing-item-content h3 {
	margin-bottom: 10px;
	font-size: 17px;
	line-height: 1.3;
}

.elementor-widget-listeo-listings .listing-item-content span,
.elementor-widget-listeo-listings-carousel .listing-item-content span {
	font-size: 14px;
	line-height: 1.4;
}

.elementor-widget-listeo-listings .listing-item-tags,
.elementor-widget-listeo-listings .listing-item-content .region,
.elementor-widget-listeo-listings-carousel .listing-item-tags,
.elementor-widget-listeo-listings-carousel .listing-item-content .region {
	display: none;
}

.elementor-widget-listeo-listings .listing-item-container .star-rating,
.elementor-widget-listeo-listings-carousel .listing-item-container .star-rating {
	padding-top: 5px;
}

.elementor-widget-listeo-listings .listing-item img,
.elementor-widget-listeo-listings-carousel .listing-item img {
	object-fit: contain;
	background: #fff;
	aspect-ratio: 1.5 / 1;
}

.elementor-widget-listeo-listings .col-lg-4.col-md-6,
.elementor-widget-listeo-listings-carousel .col-lg-4.col-md-6 {
	flex: 1;
}

.elementor-widget-listeo-listings #listeo-listings-container,
.elementor-widget-listeo-listings-carousel #listeo-listings-container {
	display: flex;
	flex-wrap: wrap;
}

.elementor-widget-listeo-listings-categories-carousel .simple-slick-carousel .slick-slide {
	padding: 10px;
}

.custom-listings-categories-carousel-container {
	display: flex;
	position: relative;
	flex-direction: row-reverse;
	gap: 40px;
}

.custom-listing-carousels {
	width: 73%;
}

.custom-listing-carousels .listing-categories-carousel-parent:first-child h2.listing-categories-carousel-parent-title {
	padding-top: 0;
}

.custom-list-container {
	flex: 1;
	position: relative;
}

.custom-taxonomy-list {
	display: flex;
	gap: 6px;
	flex-direction: column;
	max-height: calc(100vh - 220px);
	position: sticky;
	top: 180px;
	overflow-y: auto;
}

.custom-taxonomy-list ul {
	display: flex;
	flex-direction: column;
	margin-left: 16px;
}

.custom-taxonomy-list,
.custom-taxonomy-list ul {
	list-style: none;
	padding-left: 0;
}

.custom-taxonomy-list svg {
	height: 16px !important;
}

.custom-taxonomy-list ul {
	margin-left: 15px;
}

.custom-taxonomy-list ul li {
	margin-bottom: 0;
	margin-top: 6px;
}

.custom-taxonomy-list .child-category-wrap {
	display: grid;
	grid-template-rows: 0fr;
	transition: grid-template-rows 500ms;
}

.custom-taxonomy-list li:hover .child-category-wrap,
.custom-taxonomy-list .parent-listing-category.active .child-category-wrap {
	grid-template-rows: 1fr;
}

.child-listing-categories {
	overflow: hidden;
}

.custom-taxonomy-list a {
	display: inline-flex;
	align-items: center;
	gap: 12px;
	background-color: #F8F8F8;
	padding: 4px 12px;
	border-radius: 999px;
}

.custom-taxonomy-list span {
	font-size: 12px;
	color: #777;
	transition: 0.3s;
	font-weight: 500;
	word-break: break-word;
	line-height: 1.5;
	margin-left: 0;
}

.custom-taxonomy-list i {
	display: flex;
	margin: 0;
}

.custom-taxonomy-list li:hover .fa-angle-right {
	transform: rotate(90deg);
}

.custom-taxonomy-list li .fa-angle-right {
	transition: 0.3s ease all;
}

.custom-taxonomy-list rect#_Transparent_Rectangle_ {
	display: none;
}

.custom-taxonomy-list .cls-1,
.custom-taxonomy-list .cls-2,
.custom-taxonomy-list path[stroke],
.custom-taxonomy-list circle[stroke] {
	stroke: #4dd6b8;
	fill: transparent !important;
	transition: 0;
}

.custom-taxonomy-list a:hover,
.custom-taxonomy-list li.active>a {
	background-color: #4dd6b8;
}

.custom-taxonomy-list a:hover span,
.custom-taxonomy-list li.active>a span,
.custom-taxonomy-list a:hover i,
.custom-taxonomy-list li.active>a i {
	color: #fff;
}

.custom-taxonomy-list a:hover g,
.custom-taxonomy-list a:hover circle,
.custom-taxonomy-list a:hover rect,
.custom-taxonomy-list a:hover path,
.custom-taxonomy-list li.active>a g,
.custom-taxonomy-list li.active>a circle,
.custom-taxonomy-list li.active>a rect,
.custom-taxonomy-list li.active>a path {
	fill: #fff;
}

.custom-taxonomy-list a:hover .cls-1,
.custom-taxonomy-list a:hover .cls-2,
.custom-taxonomy-list a:hover path[stroke],
.custom-taxonomy-list a:hover circle[stroke],
.custom-taxonomy-list li.active>a .cls-1,
.custom-taxonomy-list li.active>a .cls-2,
.custom-taxonomy-list li.active>a path[stroke],
.custom-taxonomy-list li.active>a circle[stroke] {
	stroke: #fff;
}

.custom-taxonomy-list svg[stroke] {
	stroke: #4dd6b8;
	transition: 0;
}

.custom-taxonomy-list a:hover svg[stroke],
.custom-taxonomy-list li.active>a svg[stroke] {
	stroke: #fff;
}

.custom-taxonomy-list svg[stroke]>path {
	fill: transparent !important;
}

.open-taxonomy-list-btn {
	display: none;
	padding: 8px 20px;
	align-items: center;
	background-color: #4DD6B8;
	border-radius: 99px;
	border: none;
	left: 30px !important;
	bottom: auto;
	top: 0 !important;
	width: fit-content !important;
	position: absolute;
	order: -1;
	opacity: 0;
}

.open-taxonomy-list-btn[style*="fixed"] {
	opacity: 1;
	top: auto !important;
	bottom: 30px;
}

.open-taxonomy-list-btn:hover {
	background-color: #FFFFFF;
}

.open-taxonomy-list-btn .open-taxonomy-list-btn-icon {
	display: flex;
}

.open-taxonomy-list-btn .open-taxonomy-list-btn-icon svg {
	transition: fill 0.3s;
	fill: #FFFFFF;
	margin-right: 5.5px;
	width: 22px;
	height: 22px;
}

.open-taxonomy-list-btn:hover .open-taxonomy-list-btn-icon svg {
	fill: #4DD6B8;
}

.open-taxonomy-list-btn .open-taxonomy-list-btn-text {
	align-self: center;
	padding-left: 5px;
	font-family: "Roboto", Sans-serif;
	font-weight: 400;
	text-transform: uppercase;
	color: #FFFFFF;
	transition: color 0.3s;
	font-size: 18px;
}

.open-taxonomy-list-btn:hover .open-taxonomy-list-btn-text {
	color: #4DD6B8;
}

g#invisible_box,
rect#_Transparent_Rectangle_ {
	display: none;
}

.rating-counter.no-reviews {
	display: none;
}

.listing-categories-carousel-title {
	margin: 0;
	color: #222222;
	font-family: "Poppins", Sans-serif;
	font-size: 22px;
	font-weight: 500;
	flex: 1;
	text-wrap-style: pretty;
}

.listing-categories-carousel-header {
	background-color: #FFF4AC;
	padding: 10px 20px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 20px;
	min-height: 70px;
}

.listing-categories-carousel-parent-title {
	font-weight: 700;
	text-align: center;
	padding-bottom: 40px;
	padding-top: 40px;
	margin: 0;
}

.listing-categories-carousel-parent~.listing-categories-carousel-parent .listing-categories-carousel-parent-title {
	padding-bottom: 40px;
	padding-top: 40px;
	border-top: 4px solid #434343;
}

.listing-categories-carousel-parent~.listing-categories-carousel-parent {
	padding-top: 60px;
}

.listing-categories-carousel-button {
	background-color: #FFFFFF;
	font-family: "Roboto", Sans-serif;
	font-size: 18px;
	font-weight: 600;
	line-height: 26px;
	fill: #0A0A0A;
	color: #0A0A0A;
	border-radius: 50px 50px 50px 50px;
	padding: 12px 32px 12px 32px;
}

.elementor-widget-listeo-listings-categories-carousel {
	width: 100%;
}

.elementor-widget-listeo-listings-categories-carousel .simple-slick-carousel.slick-dotted.slick-slider.dots-nav {
	margin-bottom: 80px !important;
}

.elementor-widget-listeo-listings-categories-carousel .custom-field,
.elementor-widget-listeo-listings-carousel .custom-field {
	padding-bottom: 10px;
	margin-top: 5px;
	color: #000 !important;
	font-size: 12px !important;
	display: block;
}

.elementor-widget-listeo-listings-categories-carousel .custom-field+.custom-field,
.elementor-widget-listeo-listings-carousel .custom-field+.custom-field {
	margin-top: 0;
}

.elementor-widget-listeo-listings-categories-carousel .custom-field strong,
.elementor-widget-listeo-listings-carousel .custom-field strong {
	font-weight: 600;
}

.elementor-widget-listeo-listings-categories-carousel .simple-slick-carousel.slick-slider.dots-nav .slide-m-dots:empty {
	display: none;
}

.elementor-widget-listeo-listings-carousel.show-categories .listing-item-tags {
	display: flex;
}

.elementor-widget-listeo-listings-carousel.show-categories .listing-item-container .listing-item-tags {
	overflow: hidden;
	max-height: 105px;
	transition: max-height 0.5s cubic-bezier(0, 1, 0, 1);
	position: relative;
	margin-right: 0;
	margin-bottom: 0;
	margin-top: 12px;
	display: flex;
}

.elementor-widget-listeo-listings-carousel.show-categories .listing-item-container:hover .listing-item-tags,
.elementor-widget-listeo-listings-carousel.show-categories .listing-item-container:focus .listing-item-tags {
	transition: max-height 1s ease-in-out;
	max-height: 1000px;
}

.elementor-widget-listeo-listings-carousel.show-categories .listing-item-container .listing-item-tags::before {
	content: '';
	position: absolute;
	left: 0;
	top: 78px;
	width: 100%;
	height: 30px;
	background: linear-gradient(#F9F9F900 0%, #F9F9F9 100%);
	transition: .5s all ease-in-out
}

.elementor-widget-listeo-listings-carousel.show-categories .listing-item-container:hover .listing-item-tags::before,
.elementor-widget-listeo-listings-carousel.show-categories .listing-item-container:focus .listing-item-tags::before {
	top: 100%;
	transition: .1s all ease-in-out
}

.custom-region-search-form .main-search-input {
	margin-top: 0;
}

#listeo_core-search-form div#listeo-search-form_tax-service_category .bootstrap-select.btn-group .dropdown-toggle .filter-option {
	max-width: 280px;
}

div#listeo-search-form_tax-service_category .level-0 {
	font-weight: 700;
}

.elementor-widget-listeo-listings-categories-carousel .listing-item-container .star-rating {
	font-size: 11px;
}

.elementor-widget-listeo-listings-categories-carousel .listing-item-container .star-rating .rating-counter {
	font-size: 12px;
}

.elementor-widget-listeo-listings-categories-carousel .simple-fw-slick-carousel {
	margin-left: 0;
	margin-right: 0;
}

.pricing-package li.custom_listing_field {
	color: #000000;
}

.pricing-package .pricing-package-price strong {
	font-size: 24px;
	line-height: 150%;
}

#navigation.style-1>ul>li>ul>li>a {
	padding-top: 8px !important;
	padding-bottom: 8px !important;
}

#navigation.style-1 li:not(.menu-item-has-children)>a {
	padding-right: 15px !important;

}

/* .woocommerce-checkout .woocommerce table.shop_table.shop_table.woocommerce-checkout-review-order-table td.product-name {
	display: flex;
	flex-wrap: wrap;
}

.checkout-product-subscription-price {
	font-size: 85% !important;
}

.woocommerce-checkout .woocommerce table.shop_table.shop_table.woocommerce-checkout-review-order-table dl.variation {
	width: 100%;
}

.woocommerce-checkout .woocommerce table.shop_table.shop_table.woocommerce-checkout-review-order-table tr.wps_recurring_bifurcation_wrapper {
	display: none;
} */

.checkout-product-subscription-trial-end p {
	font-size: 17px
}

.trial-checkout-note {
	color: #777;
}

.trial-checkout-note strong {
	color: #000;
}

.pricing-package span.subscription-details {
	display: initial;
	font-size: inherit;
	color: inherit;
	font-weight: inherit;
}

/* .woocommerce-checkout .recurring-totals,
.woocommerce-checkout .cart-subtotal.recurring-total,
.woocommerce-checkout .order-total.recurring-total,
.woocommerce-checkout table.shop_table tr.cart_item .product-name .product-quantity,
.woocommerce-checkout table.shop_table td.product-total * {
	display: none !important;
}

.woocommerce-checkout table.shop_table td.product-total::before {
	content: '$0.00';
}

.woocommerce-checkout table.shop_table .checkout-product-name-container {
	margin-bottom: -30px;
} */

.woocommerce-checkout .woocommerce-checkout-review-order-table tr.cart-subtotal,
.woocommerce-checkout .woocommerce-checkout-review-order-table th.product-total {
	display: none;
}

.woocommerce-checkout .woocommerce-checkout-review-order-table span.subscription-details {
	display: contents;
}

.woocommerce-checkout .woocommerce-checkout-review-order-table .order-total th,
.woocommerce-checkout .woocommerce-checkout-review-order-table .order-total td {
	padding-top: 15px !important;
}

.woocommerce-checkout .payment-heading-container img {
	border-radius: 4px;
	margin-top: 20px;
}

.woocommerce-checkout .payment-heading-container {
	display: flex;
	align-items: flex-start;
	gap: 10px;
}

.woocommerce-checkout p.payment-heading-description {
	font-size: 13px;
	line-height: 1.65;
}

.listeo_core-dashboard-action-update {
	background-color: #4dd6b8 !important;
	color: #fff !important;
}

.listing-update-notice {
	font-size: 13px;
	color: red;
	font-weight: bold;
    margin-bottom: 6px;
}

/* Mobile Menu */

@media (max-width: 1024px) {
	.menu-arrow {
		display: none;
		width: 40px;
		text-align: right;
		padding: 10px;
		font-size: 13px;
		font-weight: 500;
		color: white;
		font-style: normal;
		font-family: 'simple-line-icons';
	}

	.menu-item-has-children>.menu-arrow {
		display: block;
	}

	.menu-item-has-children .menu-arrow::before {
		content: "\e606";
	}

	.mobile-navigation-list .menu-item-has-children>a:before {
		display: none;
	}

	#mobile-nav li {
		display: flex;
	}

	#mobile-nav li a {
		flex: 1
	}
}

/* End Mobile Menu */

/* Listing submission received page */

.page-id-11.woocommerce-order-received .elementor-widget-wc-elements .woocommerce .notification.woo-summary {
	display: block;
}

.page-id-11.woocommerce-order-received .elementor-widget-wc-elements .woocommerce>* {
	display: none;
}

.page-id-11.woocommerce-order-received .elementor-widget-wc-elements .woocommerce .notification.woo-summary p {
	line-height: 1.6125;
	margin-top: 24px;
}

/* End Listing submission received page */

/* Elementor Widget Other Listings */

.elementor-widget-listeo-listing-other-listings .simple-slick-carousel,
.elementor-widget-listeo-listing-other-events .simple-slick-carousel,
.elementor-widget-listeo-listing-other-resources .simple-slick-carousel,
.elementor-widget-listeo-listing-other-products .simple-slick-carousel {
	width: auto;
	position: static;
	margin-bottom: 10px !important;
}

.elementor-widget-listeo-listing-other-listings .slider-controls-container {
	display: none;
}

.elementor-widget-listeo-listing-other-listings .simple-slick-carousel .slick-slide {
	padding: 0;
}

.elementor-widget-listeo-listing-other-listings h3.desc-headline,
.elementor-widget-listeo-listing-other-events h3.desc-headline,
.elementor-widget-listeo-listing-other-resources h3.desc-headline,
.elementor-widget-listeo-listing-other-products h3.desc-headline {
	margin-bottom: 16px !important;
	margin-top: 16px !important;
}


.elementor-widget-listeo-listing-other-listings .custom-field,
.elementor-widget-listeo-listing-other-events .custom-field,
.elementor-widget-listeo-listing-other-resources .custom-field,
.elementor-widget-listeo-listing-other-products .custom-field {
	padding-bottom: 10px;
	margin-top: 5px;
	color: #888 !important;
	font-size: 12px !important;
	display: block;
}

.elementor-widget-listeo-listing-other-listings .custom-field+.custom-field,
.elementor-widget-listeo-listing-other-events .custom-field+.custom-field,
.elementor-widget-listeo-listing-other-resources .custom-field+.custom-field,
.elementor-widget-listeo-listing-other-products .custom-field+.custom-field {
	margin-top: 0;
}

.elementor-widget-listeo-listing-other-listings .custom-field strong,
.elementor-widget-listeo-listing-other-events .custom-field strong,
.elementor-widget-listeo-listing-other-resources .custom-field strong,
.elementor-widget-listeo-listing-other-products .custom-field strong {
	font-weight: 600;
}

.elementor-widget-listeo-listing-other-listings .listing-item-container,
.elementor-widget-listeo-listing-other-events .listing-item-container,
.elementor-widget-listeo-listing-other-resources .listing-item-container,
.elementor-widget-listeo-listing-other-products .listing-item-container {
	background: #F9F9F9;
	display: block;
	max-height: none !important;
	margin-bottom: 0;
}

.elementor-widget-listeo-listing-other-listings .listing-item-content,
.elementor-widget-listeo-listing-other-events .listing-item-content,
.elementor-widget-listeo-listing-other-resources .listing-item-content,
.elementor-widget-listeo-listing-other-products .listing-item-content {
	position: static;
	padding-top: 15px;
	padding-left: 15px;
	padding-right: 15px;
	padding-bottom: 15px;
}

.elementor-widget-listeo-listing-other-listings .listing-item-container .star-rating,
.elementor-widget-listeo-listing-other-events .listing-item-container .star-rating,
.elementor-widget-listeo-listing-other-resources .listing-item-container .star-rating,
.elementor-widget-listeo-listing-other-products .listing-item-container .star-rating {
	padding-left: 12px;
	padding-right: 12px;
	font-size: 15px;
}

.elementor-widget-listeo-listing-other-listings .listing-item,
.elementor-widget-listeo-listing-other-events .listing-item,
.elementor-widget-listeo-listing-other-resources .listing-item,
.elementor-widget-listeo-listing-other-products .listing-item {
	height: auto;
	background: none;
	background: #F9F9F9;
}

.elementor-widget-listeo-listing-other-listings .listing-item::before,
.elementor-widget-listeo-listing-other-events .listing-item::before,
.elementor-widget-listeo-listing-other-resources .listing-item::before,
.elementor-widget-listeo-listing-other-products .listing-item::before {
	display: none;
}

.elementor-widget-listeo-listing-other-listings .listing-item-content h3,
.elementor-widget-listeo-listing-other-events .listing-item-content h3,
.elementor-widget-listeo-listing-other-resources .listing-item-content h3,
.elementor-widget-listeo-listing-other-products .listing-item-content h3,
.elementor-widget-listeo-listing-other-listings .listing-item-content span,
.elementor-widget-listeo-listing-other-events .listing-item-content span,
.elementor-widget-listeo-listing-other-resources .listing-item-content span,
.elementor-widget-listeo-listing-other-products .listing-item-content span {
	color: #000;
}

.elementor-widget-listeo-listing-other-listings .listing-item-content h3,
.elementor-widget-listeo-listing-other-events .listing-item-content h3,
.elementor-widget-listeo-listing-other-resources .listing-item-content h3,
.elementor-widget-listeo-listing-other-products .listing-item-content h3 {
	margin-bottom: 10px;
	font-size: 17px;
	line-height: 1.3;
}

.elementor-widget-listeo-listing-other-listings .listing-item-content span,
.elementor-widget-listeo-listing-other-events .listing-item-content span,
.elementor-widget-listeo-listing-other-resources .listing-item-content span,
.elementor-widget-listeo-listing-other-products .listing-item-content span {
	font-size: 14px;
	line-height: 1.4;
}

.elementor-widget-listeo-listing-other-listings .listing-item-container .star-rating,
.elementor-widget-listeo-listing-other-events .listing-item-container .star-rating,
.elementor-widget-listeo-listing-other-resources .listing-item-container .star-rating,
.elementor-widget-listeo-listing-other-products .listing-item-container .star-rating {
	padding-top: 5px;
}

.elementor-widget-listeo-listing-other-listings .listing-item>img,
.elementor-widget-listeo-listing-other-events .listing-item>img,
.elementor-widget-listeo-listing-other-resources .listing-item>img,
.elementor-widget-listeo-listing-other-products .listing-item>img {
	object-fit: contain;
	background: #fff;
	aspect-ratio: 1.7 / 1;
}

.elementor-widget-listeo-listing-other-listings .listing-item-container .listing-item-tags,
.elementor-widget-listeo-listing-other-events .listing-item-container .listing-item-tags,
.elementor-widget-listeo-listing-other-resources .listing-item-container .listing-item-tags,
.elementor-widget-listeo-listing-other-products .listing-item-container .listing-item-tags {
	overflow: hidden;
	max-height: 75px;
	transition: max-height 0.5s cubic-bezier(0, 1, 0, 1);
	position: relative;
}

.elementor-widget-listeo-listing-other-listings .listing-item-container:hover .listing-item-tags,
.elementor-widget-listeo-listing-other-events .listing-item-container:hover .listing-item-tags,
.elementor-widget-listeo-listing-other-resources .listing-item-container:hover .listing-item-tags,
.elementor-widget-listeo-listing-other-products .listing-item-container:hover .listing-item-tags,
.elementor-widget-listeo-listing-other-listings .listing-item-container:focus .listing-item-tags,
.elementor-widget-listeo-listing-other-events .listing-item-container:focus .listing-item-tags,
.elementor-widget-listeo-listing-other-resources .listing-item-container:focus .listing-item-tags,
.elementor-widget-listeo-listing-other-products .listing-item-container:focus .listing-item-tags {
	transition: max-height 1s ease-in-out;
	max-height: 1000px;
}

.elementor-widget-listeo-listing-other-listings .listing-item-container .listing-item-tags::before,
.elementor-widget-listeo-listing-other-events .listing-item-container .listing-item-tags::before,
.elementor-widget-listeo-listing-other-resources .listing-item-container .listing-item-tags::before,
.elementor-widget-listeo-listing-other-products .listing-item-container .listing-item-tags::before {
	content: '';
	position: absolute;
	left: 0;
	top: 46px;
	width: 100%;
	height: 30px;
	background: linear-gradient(#ffffff00 0%, #ffffff 100%);
	transition: .5s all ease-in-out
}

.elementor-widget-listeo-listing-other-listings .listing-item-container:hover .listing-item-tags::before,
.elementor-widget-listeo-listing-other-events .listing-item-container:hover .listing-item-tags::before,
.elementor-widget-listeo-listing-other-resources .listing-item-container:hover .listing-item-tags::before,
.elementor-widget-listeo-listing-other-products .listing-item-container:hover .listing-item-tags::before,
.elementor-widget-listeo-listing-other-listings .listing-item-container:focus .listing-item-tags::before,
.elementor-widget-listeo-listing-other-events .listing-item-container:focus .listing-item-tags::before,
.elementor-widget-listeo-listing-other-resources .listing-item-container:focus .listing-item-tags::before,
.elementor-widget-listeo-listing-other-products .listing-item-container:focus .listing-item-tags::before {
	top: 100%;
	transition: .1s all ease-in-out
}

.elementor-widget-listeo-listing-other-listings .listing-item-container .listing-item-tags,
.elementor-widget-listeo-listing-other-events .listing-item-container .listing-item-tags,
.elementor-widget-listeo-listing-other-resources .listing-item-container .listing-item-tags,
.elementor-widget-listeo-listing-other-products .listing-item-container .listing-item-tags {
	margin-right: 0;
	margin-bottom: 0;
	margin-top: 12px;
}

.elementor-widget-listeo-listing-other-listings .listing-item-content .region,
.elementor-widget-listeo-listing-other-events .listing-item-content .region,
.elementor-widget-listeo-listing-other-resources .listing-item-content .region,
.elementor-widget-listeo-listing-other-products .listing-item-content .region {
	display: none;
}

.elementor-widget-listeo-listing-other-events .simple-slick-carousel .slider-controls,
.elementor-widget-listeo-listing-other-resources .simple-slick-carousel .slider-controls,
.elementor-widget-listeo-listing-other-products .simple-slick-carousel .slider-controls {
	display: flex;
}

.elementor-widget-listeo-listing-other-events .simple-slick-carousel .slider-controls button,
.elementor-widget-listeo-listing-other-resources .simple-slick-carousel .slider-controls button,
.elementor-widget-listeo-listing-other-products .simple-slick-carousel .slider-controls button {
	position: absolute;
	z-index: 11;
	padding: 7px 3px;
	border-radius: 50px;
	height: 30px;
	background-size: auto 50%;
	width: 30px;
	background-color: #4dd6b8;
	background-blend-mode: color-dodge;
	top: -260px;
}

.elementor-widget-listeo-listing-other-events .simple-slick-carousel .slider-controls,
.elementor-widget-listeo-listing-other-resources .simple-slick-carousel .slider-controls,
.elementor-widget-listeo-listing-other-products .simple-slick-carousel .slider-controls {
	background: transparent;
	padding: 0;
	border-radius: 0;
}

.elementor-widget-listeo-listing-other-events .simple-slick-carousel .slider-controls .slide-m-dots,
.elementor-widget-listeo-listing-other-resources .simple-slick-carousel .slider-controls .slide-m-dots,
.elementor-widget-listeo-listing-other-products .simple-slick-carousel .slider-controls .slide-m-dots {
	background: rgba(0, 0, 0, 0.03);
	padding: 7px 6px;
	border-radius: 50px;
}

.elementor-widget-listeo-listing-other-events .simple-slick-carousel .slider-controls .slide-m-dots .slick-dots,
.elementor-widget-listeo-listing-other-resources .simple-slick-carousel .slider-controls .slide-m-dots .slick-dots,
.elementor-widget-listeo-listing-other-products .simple-slick-carousel .slider-controls .slide-m-dots .slick-dots {
	display: flex !important;
}

.elementor-widget-listeo-listing-other-events .simple-slick-carousel .slider-controls button.slide-m-prev,
.elementor-widget-listeo-listing-other-resources .simple-slick-carousel .slider-controls button.slide-m-prev,
.elementor-widget-listeo-listing-other-products .simple-slick-carousel .slider-controls button.slide-m-prev {
	left: -8px;
	transform: rotate(180deg);
}

.elementor-widget-listeo-listing-other-events .simple-slick-carousel .slider-controls button.slide-m-next,
.elementor-widget-listeo-listing-other-resources .simple-slick-carousel .slider-controls button.slide-m-next,
.elementor-widget-listeo-listing-other-products .simple-slick-carousel .slider-controls button.slide-m-next {
	right: -8px;
}

.elementor-widget-listeo-listing-other-events .simple-slick-carousel .slider-controls-container,
.elementor-widget-listeo-listing-other-resources .simple-slick-carousel .slider-controls-container,
.elementor-widget-listeo-listing-other-products .simple-slick-carousel .slider-controls-container {
	position: static;
	margin-top: 16px;
	width: 100%;
}

.elementor-widget-listeo-listing-other-events .simple-slick-carousel,
.elementor-widget-listeo-listing-other-resources .simple-slick-carousel,
.elementor-widget-listeo-listing-other-products .simple-slick-carousel {
	display: flex !important;
	flex-direction: column;
}

@media (max-width:767px) {

	.elementor-widget-listeo-listing-other-listings .listing-small-badges-container .listing-small-badge,
	.elementor-widget-listeo-listing-other-events .listing-small-badges-container .listing-small-badge,
	.elementor-widget-listeo-listing-other-resources .listing-small-badges-container .listing-small-badge,
	.elementor-widget-listeo-listing-other-products .listing-small-badges-container .listing-small-badge {
		font-size: 12px;
	}

	.elementor-widget-listeo-listing-other-listings .listing-small-badges-container,
	.elementor-widget-listeo-listing-other-events .listing-small-badges-container,
	.elementor-widget-listeo-listing-other-resources .listing-small-badges-container,
	.elementor-widget-listeo-listing-other-products .listing-small-badges-container {
		left: 15px;
	}

	.elementor-widget-listeo-listing-other-listings .listing-ndis-container,
	.elementor-widget-listeo-listing-other-events .listing-ndis-container,
	.elementor-widget-listeo-listing-other-resources .listing-ndis-container,
	.elementor-widget-listeo-listing-other-products .listing-ndis-container {
		right: 15px;
	}

	.elementor-widget-listeo-listing-other-listings .listing-ndis-container img,
	.elementor-widget-listeo-listing-other-events .listing-ndis-container img,
	.elementor-widget-listeo-listing-other-resources .listing-ndis-container img,
	.elementor-widget-listeo-listing-other-products .listing-ndis-container img {
		width: 50px !important;
	}
}

/* End Elementor Widget Other Listings */

/* Progress Steps */

.progress-container {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 700px;
	margin: 20px auto;
	position: relative;
}

.progress-bar {
	display: flex;
	align-items: center;
	width: 100%;
	position: relative;
}

.progress-step {
	width: 40px;
	height: 40px;
	border-radius: 50%;
	background: #fff;
	border: 3px solid #aaa;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: bold;
	font-size: 16px;
	color: #555;
	position: relative;
	z-index: 2;
}

.progress-step.active {
	border-color: #4dd6b8;
	color: #000;
}

.progress-step.completed {
	border-color: #555;
	color: #000;
}

.progress-line {
	flex: 1;
	height: 3px;
	background: #aaa;
	position: relative;
	z-index: 1;
}

.progress-line.active {
	background: #4dd6b8;
}

.progress-text {
	display: flex;
	align-items: center;
	width: 100%;
	position: relative;
	justify-content: space-between;
	margin-top: 20px;
	margin-bottom: 20px;
}

.progress-step-text {
	text-align: center;
	display: flex;
	justify-content: center;
	align-items: center;
	white-space: nowrap;
	position: relative;
	width: 40px;
	font-size: 12px;
}

.progress-step-text.active {
	opacity: 1;
}

.progress-container {
	flex-direction: column;
}

.progress-step-text span {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.progress-step-text:nth-child(2n + 2) {
	margin-top: -160px;
}

@media (max-width:767px) {
	.progress-step-text {
		display: none;
	}

	.progress-step-text.active {
		display: flex;
	}

	.progress-text {
		justify-content: center;
	}
}

/* End Progress Steps */

/* Dropdown Icon */

.listeo-svg-icon-box-grid.listeo-term-icon .cls-1,
.listeo-svg-icon-box-grid.listeo-term-icon .cls-2,
.listeo-svg-icon-box-grid.listeo-term-icon path[stroke],
.listeo-svg-icon-box-grid.listeo-term-icon circle[stroke] {
	stroke: #4dd6b8;
	fill: transparent !important;
	transition: 0;
}

.listeo-svg-icon-box-grid.listeo-term-icon svg[stroke] {
	stroke: #4dd6b8;
	transition: 0;
}

.listeo-svg-icon-box-grid.listeo-term-icon svg[stroke]>path {
	fill: transparent !important;
}

.listeo-svg-icon-box-grid.listeo-term-icon svg {
	width: 16px;
	height: 16px;
}

/* End Dropdown Icon */

/* Mobile Header Search */

button.mobile-search-btn {
	float: right;
	width: 50px;
	height: 50px;
	margin-right: 67px;
	display: none;
	place-content: center;
	border-radius: 50%;
	border: none;
	font-size: 20px;
}

button.mobile-search-btn.active i {
	color: #fff;
}

button.mobile-search-btn.active {
	background: #4dd6b8;
}

button.mobile-search-btn i {
	color: #555;
}

@media (max-width:1024px) {
	#header-container .header-search-container {
		position: absolute;
		opacity: 1;
		left: 0;
		top: 100%;
		width: 100%;
		pointer-events: all;
	}

	#header-container .header-search-container.active {
		display: flex !important;
	}

	#header-container .header-search-container .gray-style .main-search-input {
		transform: none;
		border-radius: 50px;
		display: flex;
		max-height: 68px;
		overflow: hidden;
	}

	#header-container .header-search-container form#listeo_core-search-form {
		width: 100%;
		padding: 0 13px 12px;
		background: #fff;
	}

	#header-container .header-search-container .gray-style .main-search-input button.button {
		margin: 0;
		width: auto;
	}

	#header-container .header-search-container .gray-style .main-search-input .main-search-input-item.text {
		margin: 0;
	}

	#header-container .header-search-container input#keyword_search {
		border: none;
		height: 51px;
		line-height: 51px;
		box-shadow: none;
	}
}

@media (min-width:768px) and (max-width:1024px) {
	#header-container .header-search-container {
		width: 510px;
		border-radius: 30px;
		overflow: hidden;
		right: 5%;
		left: auto;
	}

	#header-container .header-search-container form#listeo_core-search-form {
		padding: 12px 20px;
	}

	.slim-mobile-menu #logo {
		max-width: calc(100% - 175px);
	}
}

@media (max-width:1024px) {
	button.mobile-search-btn {
		display: grid;
	}
}

/* End Mobile Header Search */

/* Footer */

#footer ul.menu {
	width: auto;
}

@media (min-width: 992px) {
	#footer .col-md-3:first-child {
		width: 35%;
	}

	#footer .col-md-3 {
		width: 21.6%;
	}
}

@media (max-width: 991px) {
	#footer .row {
		display: flex;
		flex-wrap: wrap;
	}
}

/* End Footer */

@media (max-width:1023px) {

	.elementor-widget-listeo-listings .col-lg-4.col-md-6,
	.elementor-widget-listeo-listings-carousel .col-lg-4.col-md-6 {
		min-width: 40%;
	}

	.elementor-widget-listeo-listings #listeo-listings-container,
	.elementor-widget-listeo-listings-carousel #listeo-listings-container {
		row-gap: 30px;
	}

	.elementor-widget-listeo-listings-categories-carousel {
		overflow: hidden;
	}

	.custom-list-container {
		background: #fff;
		display: none;
		top: auto !important;
		bottom: 100px;
		width: calc(100% - 20px) !important;
		max-height: calc(100vh - 160px);
		position: fixed !important;
		z-index: 1;
		left: 10px;
		padding: 20px;
	}

	.open-taxonomy-list-btn {
		display: flex;
	}

	.custom-listings-categories-carousel-container {
		flex-direction: column;
	}

	.custom-listing-carousels {
		width: 100%;
		padding-left: 15px;
		padding-right: 15px;
	}
}

@media (max-width:767px) {

	.elementor-widget-listeo-listings .col-lg-4.col-md-6,
	.elementor-widget-listeo-listings-carousel .col-lg-4.col-md-6 {
		min-width: 90%;
	}

	.listing-categories-carousel-header {
		flex-direction: column;
		gap: 10px;
		padding: 20px;
	}

	.elementor-widget-listeo-listings-categories-carousel .simple-slick-carousel.slick-dotted.slick-slider.dots-nav {
		margin-bottom: 90px !important;
	}
}

@media (min-width:1024px) {

	.simple-fw-slick-carousel .slider-controls,
	.elementor-widget-listeo-listings-categories-carousel .simple-slick-carousel .slider-controls {
		display: flex;
	}

	.simple-fw-slick-carousel .slider-controls button,
	.elementor-widget-listeo-listings-categories-carousel .simple-slick-carousel .slider-controls button {
		position: absolute;
		z-index: 11;
		padding: 7px 3px;
		border-radius: 50px;
		height: 30px;
		background-size: auto 50%;
		width: 30px;
		background-color: #4dd6b8;
		background-blend-mode: color-dodge;
		top: -220px;
	}

	.simple-fw-slick-carousel .slider-controls,
	.elementor-widget-listeo-listings-categories-carousel .simple-slick-carousel .slider-controls {
		background: transparent;
		padding: 0;
		border-radius: 0;
	}

	.simple-fw-slick-carousel .slider-controls .slide-m-dots,
	.elementor-widget-listeo-listings-categories-carousel .simple-slick-carousel .slider-controls .slide-m-dots {
		background: rgba(0, 0, 0, 0.03);
		padding: 7px 6px;
		border-radius: 50px;
	}

	.simple-fw-slick-carousel .slider-controls button.slide-m-prev,
	.elementor-widget-listeo-listings-categories-carousel .simple-slick-carousel .slider-controls button.slide-m-prev {
		left: -8px;
		transform: rotate(180deg);
	}

	.simple-fw-slick-carousel .slider-controls button.slide-m-next,
	.elementor-widget-listeo-listings-categories-carousel .simple-slick-carousel .slider-controls button.slide-m-next {
		right: -8px;
	}

	.simple-fw-slick-carousel .slider-controls-container,
	.elementor-widget-listeo-listings-categories-carousel .simple-slick-carousel .slider-controls-container {
		bottom: -48px;
	}

	div[data-elementor-type="wp-page"] {
		overflow: visible;
	}
}

@media (max-width:1024px) {
	.custom-taxonomy-list {
		gap: 16px 12px;
	}

	.custom-region-search-form .dropdown-toggle {
		border-radius: 55px !important;
	}
}

@media (max-width:1023px) {
	.slider-controls-container {
		bottom: -48px;
	}
}

/* Hide like button */
.simple-slick-carousel .fw-carousel-item .listing-item-content {
	padding-right: 25px;
}

.simple-slick-carousel .fw-carousel-item .save.like-icon,
.listing-item-container.list-layout span.like-icon {
	display: none;
}

/* End Hide like button */