<?php

/**
 * Awesomesauce class.
 *
 * @category   Class
 * @package    ElementorAwesomesauce
 * @subpackage WordPress
 * <AUTHOR> <<EMAIL>>
 * @copyright  2020 <PERSON>
 * @license    https://opensource.org/licenses/GPL-3.0 GPL-3.0-only
 * @link       link(https://www.benmarshall.me/build-custom-elementor-widgets/,
 *             Build Custom Elementor Widgets)
 * @since      1.0.0
 * php version 7.3.9
 */

namespace ElementorListeo\Widgets;

use Elementor\Widget_Base;
use Elementor\Controls_Manager;
use Elementor\Utils;

if ( ! defined( 'ABSPATH' ) ) {
	// Exit if accessed directly.
	exit;
}

/**
 * Awesomesauce widget class.
 *
 * @since 1.0.0
 */
class ListingOtherEvents extends Widget_Base {

	/**
	 * Retrieve the widget name.
	 *
	 * @since 1.0.0
	 *
	 * @access public
	 *
	 * @return string Widget name.
	 */
	public function get_name() {
		return 'listeo-listing-other-events';
	}

	/**
	 * Retrieve the widget title.
	 *
	 * @since 1.0.0
	 *
	 * @access public
	 *
	 * @return string Widget title.
	 */
	public function get_title() {
		return __( 'Listing My Other Events', 'listeo_elementor' );
	}

	/**
	 * Retrieve the widget icon.
	 *
	 * @since 1.0.0
	 *
	 * @access public
	 *
	 * @return string Widget icon.
	 */
	public function get_icon() {
		return 'eicon-faq';
	}

	/**
	 * Retrieve the list of categories the widget belongs to.
	 *
	 * Used to determine where to display the widget in the editor.
	 *
	 * Note that currently Elementor supports only one category.
	 * When multiple categories passed, Elementor uses the first one.
	 *
	 * @since 1.0.0
	 *
	 * @access public
	 *
	 * @return array Widget categories.
	 */
	public function get_categories() {
		return array( 'listeo-single' );
	}

	/**
	 * Register the widget controls.
	 *
	 * Adds different input fields to allow the user to change and customize the widget settings.
	 *
	 * @since 1.0.0
	 *
	 * @access protected
	 */
	protected function register_controls() {
		// 'title' 		=> 'Service Title',
		// 	    'url' 			=> '',
		// 	    'url_title' 	=> '',

		// 	   	'icon'          => 'im im-icon-Office',
		// 	    'type'			=> 'box-1', // 'box-1, box-1 rounded, box-2, box-3, box-4'
		// 	    'with_line' 	=> 'yes',


		$this->start_controls_section(
			'section_content',
			array(
				'label' => __( 'Content', 'listeo_elementor' ),
			)
		);

		$custom_fields = $this->get_fields();

		$dropdown = [ '' => 'None' ];
		foreach ( $custom_fields as $field ) {
			$dropdown[ $field['id'] ] = $field['name'];
		}


		$this->add_control(
			'custom_field_heading',
			[ 
				'label' => __( 'Custom Field Heading', 'listeo_elementor' ),
				'type' => Controls_Manager::TEXT,
				'default' => '',
			]
		);

		$this->add_control(
			'custom_field',
			[ 
				'label' => __( 'Custom Field', 'listeo_elementor' ),
				'type' => Controls_Manager::SELECT,
				'options' => $dropdown,
				'default' => '',
			]
		);

		$this->add_control(
			'custom_field_heading_2',
			[ 
				'label' => __( 'Custom Field Heading 2', 'listeo_elementor' ),
				'type' => Controls_Manager::TEXT,
				'default' => '',
			]
		);

		$this->add_control(
			'custom_field_2',
			[ 
				'label' => __( 'Custom Field 2', 'listeo_elementor' ),
				'type' => Controls_Manager::SELECT,
				'options' => $dropdown,
				'default' => '',
			]
		);

		$this->add_control(
			'hide_reviews',
			[ 
				'label' => __( 'Hide reviews', 'listeo_elementor' ),
				'type' => Controls_Manager::SWITCHER,
				'label_on' => __( 'Show', 'listeo_elementor' ),
				'label_off' => __( 'Hide', 'listeo_elementor' ),
				'return_value' => 'yes',
				'default' => 'no',
			]
		);

		$this->add_control(
			'disable_autoplay',
			[ 
				'label' => __( 'Disable Autoplay', 'listeo_elementor' ),
				'type' => Controls_Manager::SWITCHER,
				'label_on' => __( 'On', 'listeo_elementor' ),
				'label_off' => __( 'Off', 'listeo_elementor' ),
				'return_value' => 'yes',
				'default' => 'no',
			]
		);

		$this->add_control(
			'displayed_items',
			array(
				'label' => __( 'Displayed Carousal Items', 'listeo_elementor' ),
				'type' => Controls_Manager::NUMBER,
				'min' => 1,
				'max' => 5,
				'step' => 1,
				'default' => 1,
			)
		);

		$this->end_controls_section();
	}

	/**
	 * Render the widget output on the frontend.
	 *
	 * Written in PHP and used to generate the final HTML.
	 *
	 * @since 1.0.0
	 *
	 * @access protected
	 */
	protected function render() {
		$settings = $this->get_settings_for_display();
		$settings['type'] = 'events';

		$template_loader = new \Listeo_Core_Template_Loader;
		$template_loader->set_template_data( [ 'settings' => $settings ] )->get_template_part( 'single-partials/single-listing', 'other' );
	}


	protected function get_fields() {
		$fields = array();
		// choose content field

		$service = \Listeo_Core_Meta_Boxes::meta_boxes_service();
		foreach ( $service['fields'] as $field ) {
			$fields[ $field['id'] ] = $field;
		}

		$location = \Listeo_Core_Meta_Boxes::meta_boxes_location();
		foreach ( $location['fields'] as $field ) {
			$fields[ $field['id'] ] = $field;
		}

		$event = \Listeo_Core_Meta_Boxes::meta_boxes_event();
		foreach ( $event['fields'] as $field ) {
			$fields[ $field['id'] ] = $field;
		}

		$prices = \Listeo_Core_Meta_Boxes::meta_boxes_prices();
		foreach ( $prices['fields'] as $field ) {
			$fields[ $field['id'] ] = $field;
		}

		$contact = \Listeo_Core_Meta_Boxes::meta_boxes_contact();
		foreach ( $contact['fields'] as $field ) {
			$fields[ $field['id'] ] = $field;
		}

		$rental = \Listeo_Core_Meta_Boxes::meta_boxes_rental();
		foreach ( $rental['fields'] as $field ) {
			$fields[ $field['id'] ] = $field;
		}

		$custom = \Listeo_Core_Meta_Boxes::meta_boxes_custom();
		foreach ( $custom['fields'] as $field ) {
			$fields[ $field['id'] ] = $field;
		}

		return $fields;
	}
}
