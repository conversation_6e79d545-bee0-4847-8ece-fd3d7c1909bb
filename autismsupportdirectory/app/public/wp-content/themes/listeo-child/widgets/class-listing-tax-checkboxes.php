<?php

/**
 * Awesomesauce class.
 *
 * @category   Class
 * @package    ElementorAwesomesauce
 * @subpackage WordPress
 * <AUTHOR> <<EMAIL>>
 * @copyright  2020 <PERSON>
 * @license    https://opensource.org/licenses/GPL-3.0 GPL-3.0-only
 * @link       link(https://www.benmarshall.me/build-custom-elementor-widgets/,
 *             Build Custom Elementor Widgets)
 * @since      1.0.0
 * php version 7.3.9
 */

namespace ElementorListeo\Widgets;

use Elementor\Widget_Base;
use Elementor\Controls_Manager;
use Elementor\Utils;

if ( ! defined( 'ABSPATH' ) ) {
	// Exit if accessed directly.
	exit;
}

/**
 * Awesomesauce widget class.
 *
 * @since 1.0.0
 */
class CustomListingTaxonomyCheckboxes extends Widget_Base {

	/**
	 * Retrieve the widget name.
	 *
	 * @since 1.0.0
	 *
	 * @access public
	 *
	 * @return string Widget name.
	 */
	public function get_name() {
		return 'listeo-listing-taxonomy-checkboxes-2';
	}

	/**
	 * Retrieve the widget title.
	 *
	 * @since 1.0.0
	 *
	 * @access public
	 *
	 * @return string Widget title.
	 */
	public function get_title() {
		return __( 'Listing Taxonomy Checkboxes(simple list)', 'listeo_elementor' );
	}

	/**
	 * Retrieve the widget icon.
	 *
	 * @since 1.0.0
	 *
	 * @access public
	 *
	 * @return string Widget icon.
	 */
	public function get_icon() {
		return 'eicon-checkbox';
	}

	/**
	 * Retrieve the list of categories the widget belongs to.
	 *
	 * Used to determine where to display the widget in the editor.
	 *
	 * Note that currently Elementor supports only one category.
	 * When multiple categories passed, Elementor uses the first one.
	 *
	 * @since 1.0.0
	 *
	 * @access public
	 *
	 * @return array Widget categories.
	 */
	public function get_categories() {
		return array( 'listeo-single' );
	}

	/**
	 * Register the widget controls.
	 *
	 * Adds different input fields to allow the user to change and customize the widget settings.
	 *
	 * @since 1.0.0
	 *
	 * @access protected
	 */
	protected function register_controls() {
		// 'title' 		=> 'Service Title',
		// 	    'url' 			=> '',
		// 	    'url_title' 	=> '',

		// 	   	'icon'          => 'im im-icon-Office',
		// 	    'type'			=> 'box-1', // 'box-1, box-1 rounded, box-2, box-3, box-4'
		// 	    'with_line' 	=> 'yes',


		$this->start_controls_section(
			'section_content',
			array(
				'label' => __( 'Content', 'listeo_elementor' ),
			)
		);

		$this->add_control(
			'taxonomy',
			[ 
				'label' => __( 'Taxonomy', 'listeo_elementor' ),
				'type' => Controls_Manager::SELECT2,
				'label_block' => true,
				'default' => [ 'listing_feature' ],
				'multiple' => true,
				'options' => $this->get_taxonomies(),

			]
		);
		//add checkbox control
		$this->add_control(
			'show_title',
			[ 
				'label' => __( 'Show with titles', 'listeo_elementor' ),
				'type' => \Elementor\Controls_Manager::SWITCHER,
				'label_on' => __( 'Show', 'listeo_elementor' ),
				'label_off' => __( 'Hide', 'listeo_elementor' ),
				'return_value' => 'yes',
				'default' => 'yes',
			]
		);

		$this->end_controls_section();
	}

	/**
	 * Render the widget output on the frontend.
	 *
	 * Written in PHP and used to generate the final HTML.
	 *
	 * @since 1.0.0
	 *
	 * @access protected
	 */
	protected function render() {
		$settings = $this->get_settings_for_display();
		global $post;


		$taxonomies = $settings['taxonomy'];

		if ( empty( $taxonomies ) ) {
			return;
		}
		foreach ( $taxonomies as $tax ) {
			$term_list = get_the_terms( $post->ID, $tax );
			$tax_obj = get_taxonomy( $tax );
			$taxonomy = get_taxonomy_labels( $tax_obj );


			if ( ! empty( $term_list ) ) { ?>
				<?php if ( $settings['show_title'] ) { ?>
					<h3 class="listing-desc-headline"><?php echo $taxonomy->name; ?></h3>

				<?php } ?>
				<ul>
					<?php

					foreach ( $term_list as $term ) {
						$term_link = get_term_link( $term );
						if ( is_wp_error( $term_link ) )
							continue;

						echo '<li><a href="' . esc_url( $term_link ) . '">' . $term->name . '</a></li>';
					}
					?>
				</ul>
			<?php }
		}
		;

	?>
	<?php
	}


	protected function get_taxonomies() {
		$taxonomies = get_object_taxonomies( 'listing', 'objects' );

		$options = [ '' => '' ];

		foreach ( $taxonomies as $taxonomy ) {
			$options[ $taxonomy->name ] = $taxonomy->label;
		}

		return $options;
	}
}