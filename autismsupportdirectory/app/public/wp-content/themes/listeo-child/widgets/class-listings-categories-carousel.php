<?php
/**
 * Awesomesauce class.
 *
 * @category   Class
 * @package    ElementorAwesomesauce
 * @subpackage WordPress
 * <AUTHOR> <<EMAIL>>
 * @copyright  2020 <PERSON>
 * @license    https://opensource.org/licenses/GPL-3.0 GPL-3.0-only
 * @link       link(https://www.benmarshall.me/build-custom-elementor-widgets/,
 *             Build Custom Elementor Widgets)
 * @since      1.0.0
 * php version 7.3.9
 */

namespace ElementorListeo\Widgets;

use Elementor\Widget_Base;
use Elementor\Controls_Manager;
use Elementor\Core\Kits\Documents\Tabs\Global_Typography;
use Elementor\Group_Control_Typography;

if ( ! defined( 'ABSPATH' ) ) {
	// Exit if accessed directly.
	exit;
}

/**
 * Awesomesauce widget class.
 *
 * @since 1.0.0
 */
class ListingsCategoriesCarousel extends Widget_Base {

	/**
	 * Retrieve the widget name.
	 *
	 * @since 1.0.0
	 *
	 * @access public
	 *
	 * @return string Widget name.
	 */
	public function get_name() {
		return 'listeo-listings-categories-carousel';
	}

	/**
	 * Retrieve the widget title.
	 *
	 * @since 1.0.0
	 *
	 * @access public
	 *
	 * @return string Widget title.
	 */
	public function get_title() {
		return __( 'Listings Categories Carousel', 'listeo_elementor' );
	}

	/**
	 * Retrieve the widget icon.
	 *
	 * @since 1.0.0
	 *
	 * @access public
	 *
	 * @return string Widget icon.
	 */
	public function get_icon() {
		return 'eicon-carousel-loop';
	}

	/**
	 * Retrieve the list of categories the widget belongs to.
	 *
	 * Used to determine where to display the widget in the editor.
	 *
	 * Note that currently Elementor supports only one category.
	 * When multiple categories passed, Elementor uses the first one.
	 *
	 * @since 1.0.0
	 *
	 * @access public
	 *
	 * @return array Widget categories.
	 */
	public function get_categories() {
		return array( 'listeo' );
	}

	/**
	 * Register the widget controls.
	 *
	 * Adds different input fields to allow the user to change and customize the widget settings.
	 *
	 * @since 1.0.0
	 *
	 * @access protected
	 */
	protected function register_controls() {
		$this->start_controls_section(
			'section_query',
			array(
				'label' => __( 'Query', 'listeo_elementor' ),
			)
		);

		$this->add_control(
			'taxonomy',
			[ 
				'label' => __( 'Taxonomy', 'elementor-pro' ),
				'type' => Controls_Manager::SELECT2,
				'label_block' => true,
				'default' => [],
				'options' => $this->get_taxonomies(),

			]
		);

		$this->add_control(
			'link_to_online_services',
			[ 
				'label' => __( 'Link to Online Services Page', 'listeo_elementor' ),
				'type' => Controls_Manager::SWITCHER,
				'label_on' => __( 'Show', 'listeo_elementor' ),
				'label_off' => __( 'Hide', 'listeo_elementor' ),
				'return_value' => 'yes',
				'default' => 'no',
				'condition' => [ 
					'taxonomy' => 'service_category',
				],
			]
		);

		$custom_fields = custom_listeo_get_fields();

		$dropdown = [ '' => 'None' ];
		foreach ( $custom_fields as $field ) {
			$dropdown[ $field['id'] ] = $field['name'];
		}

		$this->add_control(
			'custom_field_as_categories',
			[ 
				'label' => __( 'Use Custom Fields Instead of Categories', 'listeo_elementor' ),
				'type' => Controls_Manager::SELECT,
				'options' => $dropdown,
				'default' => '',
			]
		);


		$taxonomy_names = get_object_taxonomies( 'listing', 'object' );
		foreach ( $taxonomy_names as $key => $value ) {

			$this->add_control(
				$value->name . '_include',
				[ 
					'label' => __( 'Include ' . $value->label, 'listeo_elementor' ),
					'type' => Controls_Manager::SELECT2,
					'label_block' => true,
					'multiple' => true,
					'default' => [],
					'options' => $this->get_terms( $value->name ),
					'condition' => [ 
						'taxonomy' => $value->name,
						'custom_field_as_categories' => '',
					],
				]
			);
			$this->add_control(
				$value->name . '_exclude',
				[ 
					'label' => __( 'Exclude ' . $value->label, 'listeo_elementor' ),
					'type' => Controls_Manager::SELECT2,
					'label_block' => true,
					'multiple' => true,
					'default' => [],
					'options' => $this->get_terms( $value->name ),
					'condition' => [ 
						'taxonomy' => $value->name,
						'custom_field_as_categories' => '',
					],
				]
			);

		}

		$this->add_control(
			'button_text',
			[ 
				'label' => __( 'Button text', 'listeo_elementor' ),
				'type' => Controls_Manager::TEXT,
				'default' => 'View All Listings & Map',
			]
		);

		$this->add_control(
			'disable_search_sidebar',
			[ 
				'label' => __( 'Disable search sidebar', 'listeo_elementor' ),
				'type' => Controls_Manager::SWITCHER,
				'label_on' => __( 'Show', 'listeo_elementor' ),
				'label_off' => __( 'Hide', 'listeo_elementor' ),
				'return_value' => 'yes',
				'default' => 'no',
			]
		);

		$this->add_control(
			'limit',
			[ 
				'label' => __( 'Listings to display', 'listeo_elementor' ),
				'type' => Controls_Manager::NUMBER,
				'min' => 1,
				'max' => 16,
				'step' => 1,
				'default' => 6,
			]
		);

		$this->add_control(
			'orderby',
			[ 
				'label' => __( 'Listings Order by', 'listeo_elementor' ),
				'type' => Controls_Manager::SELECT,
				'options' => [ 
					'title' => 'Title',
					'date' => 'Date',
					'rand' => 'Random',
				],
				'default' => 'date',
			]
		);

		$this->add_control(
			'order',
			[ 
				'label' => __( 'Listings Order', 'listeo_elementor' ),
				'type' => Controls_Manager::SELECT,
				'options' => [ 
					'ASC' => 'Ascending',
					'DESC' => 'Descending',
				],
				'default' => 'DESC',
			]
		);

		$this->add_control(
			'feature',
			[ 
				'label' => __( 'Show only listings with features', 'listeo_elementor' ),
				'type' => Controls_Manager::SELECT2,
				'label_block' => true,
				'multiple' => true,
				'default' => [],
				'options' => $this->get_terms( 'listing_feature' ),
			]
		);

		$this->add_control(
			'regions',
			[ 
				'label' => __( 'Show only listings from regions', 'listeo_elementor' ),
				'type' => Controls_Manager::SELECT2,
				'label_block' => true,
				'multiple' => true,
				'default' => [],
				'options' => $this->get_terms( 'region' ),
			]
		);

		$this->add_control(
			'featured',
			[ 
				'label' => __( 'Show only featured listings', 'listeo_elementor' ),
				'type' => Controls_Manager::SWITCHER,
				'label_on' => __( 'Show', 'listeo_elementor' ),
				'label_off' => __( 'Hide', 'listeo_elementor' ),
				'return_value' => 'yes',
				'default' => 'no',
			]
		);

		$this->end_controls_section();

		$this->start_controls_section(
			'section_content',
			array(
				'label' => __( 'Settings', 'listeo_elementor' ),
			)
		);

		$this->add_control(
			'sidebar_title',
			[ 
				'label' => __( 'Sidebar Title', 'listeo_elementor' ),
				'type' => Controls_Manager::TEXT,
				'default' => __( 'Categories', 'listeo_elementor' ),
				'condition' => [ 
					'enable_sidebar' => 'yes',
				],
			]
		);

		$this->add_control(
			'custom_field_heading',
			[ 
				'label' => __( 'Custom Field Heading', 'listeo_elementor' ),
				'type' => Controls_Manager::TEXT,
				'default' => '',
			]
		);

		$this->add_control(
			'custom_field',
			[ 
				'label' => __( 'Custom Field', 'listeo_elementor' ),
				'type' => Controls_Manager::SELECT,
				'options' => $dropdown,
				'default' => '',
			]
		);

		$this->add_control(
			'custom_field_heading_2',
			[ 
				'label' => __( 'Custom Field Heading 2', 'listeo_elementor' ),
				'type' => Controls_Manager::TEXT,
				'default' => '',
			]
		);

		$this->add_control(
			'custom_field_2',
			[ 
				'label' => __( 'Custom Field 2', 'listeo_elementor' ),
				'type' => Controls_Manager::SELECT,
				'options' => $dropdown,
				'default' => '',
			]
		);

		$this->add_control(
			'hide_reviews',
			[ 
				'label' => __( 'Hide reviews', 'listeo_elementor' ),
				'type' => Controls_Manager::SWITCHER,
				'label_on' => __( 'Show', 'listeo_elementor' ),
				'label_off' => __( 'Hide', 'listeo_elementor' ),
				'return_value' => 'yes',
				'default' => 'no',
			]
		);

		$this->add_control(
			'autoplay',
			[ 
				'label' => __( 'Auto Play', 'listeo_elementor' ),
				'type' => Controls_Manager::SWITCHER,
				'label_on' => __( 'Show', 'listeo_elementor' ),
				'label_off' => __( 'Hide', 'listeo_elementor' ),
				'return_value' => 'yes',
				'default' => 'yes',
			]
		);


		$this->add_control(
			'autoplayspeed',
			array(
				'label' => __( 'Auto Play Speed', 'listeo_elementor' ),
				'type' => Controls_Manager::NUMBER,
				'min' => 1000,
				'max' => 10000,
				'step' => 500,
				'default' => 3000,
			)
		);

		$this->end_controls_section();

		$this->start_controls_section(
			'section_parent_title_style',
			[ 
				'label' => esc_html__( 'Parent Heading', 'elementor' ),
				'tab' => Controls_Manager::TAB_STYLE,
			]
		);

		$this->add_responsive_control(
			'parent_title_align',
			[ 
				'label' => esc_html__( 'Alignment', 'elementor' ),
				'type' => Controls_Manager::CHOOSE,
				'options' => [ 
					'left' => [ 
						'title' => esc_html__( 'Left', 'elementor' ),
						'icon' => 'eicon-text-align-left',
					],
					'center' => [ 
						'title' => esc_html__( 'Center', 'elementor' ),
						'icon' => 'eicon-text-align-center',
					],
					'right' => [ 
						'title' => esc_html__( 'Right', 'elementor' ),
						'icon' => 'eicon-text-align-right',
					],
					'justify' => [ 
						'title' => esc_html__( 'Justified', 'elementor' ),
						'icon' => 'eicon-text-align-justify',
					],
				],
				'default' => '',
				'selectors' => [ 
					'{{WRAPPER}} .listing-categories-carousel-parent-title' => 'text-align: {{VALUE}};',
				],
				'separator' => 'after',
			]
		);

		$this->add_group_control(
			Group_Control_Typography::get_type(),
			[ 
				'name' => 'parent_typography',
				'global' => [ 
					'default' => Global_Typography::TYPOGRAPHY_PRIMARY,
				],
				'selector' => '{{WRAPPER}} .listing-categories-carousel-parent-title',
			]
		);

		$this->end_controls_section();

		$this->start_controls_section(
			'section_child_style',
			[ 
				'label' => esc_html__( 'Child Heading', 'elementor' ),
				'tab' => Controls_Manager::TAB_STYLE,
			]
		);

		$this->add_responsive_control(
			'child_title_align',
			[ 
				'label' => esc_html__( 'Alignment', 'elementor' ),
				'type' => Controls_Manager::CHOOSE,
				'options' => [ 
					'left' => [ 
						'title' => esc_html__( 'Left', 'elementor' ),
						'icon' => 'eicon-text-align-left',
					],
					'center' => [ 
						'title' => esc_html__( 'Center', 'elementor' ),
						'icon' => 'eicon-text-align-center',
					],
					'right' => [ 
						'title' => esc_html__( 'Right', 'elementor' ),
						'icon' => 'eicon-text-align-right',
					],
					'justify' => [ 
						'title' => esc_html__( 'Justified', 'elementor' ),
						'icon' => 'eicon-text-align-justify',
					],
				],
				'default' => '',
				'selectors' => [ 
					'{{WRAPPER}} .listing-categories-carousel-title' => 'text-align: {{VALUE}};',
				],
				'separator' => 'after',
			]
		);

		$this->add_group_control(
			Group_Control_Typography::get_type(),
			[ 
				'name' => 'child_title_typography',
				'global' => [ 
					'default' => Global_Typography::TYPOGRAPHY_PRIMARY,
				],
				'selector' => '{{WRAPPER}} .listing-categories-carousel-title',
			]
		);

		$this->end_controls_section();
	}

	/**
	 * Render the widget output on the frontend.
	 *
	 * Written in PHP and used to generate the final HTML.
	 *
	 * @since 1.0.0
	 *
	 * @access protected
	 */
	protected function render() {
		$settings = $this->get_settings_for_display();

		$limit = $settings['limit'];

		$taxonomy = $settings['taxonomy'];

		$regions = $settings['regions'];

		$feature = $settings['feature'];

		$custom_fields_as_categories = $settings['custom_field_as_categories'];

		$list_items = [];

		if ( empty( $taxonomy ) ) {
			$taxonomy = "listing_category";
		}

		$parents_args = array(
			'taxonomy' => $settings['taxonomy'],
			'orderby' => 'name',
			'order' => 'ASC',
			'hide_empty' => true,
			'number' => 99,
			'parent' => 0,
		);

		if ( empty( $custom_fields_as_categories ) ) {
			$parents_args['include'] = $settings[ $taxonomy . '_include' ];
			$parents_args['exclude'] = $settings[ $taxonomy . '_exclude' ];
		}

		$parents = get_terms( $parents_args );

		$parents = sort_categories_by_alt_name( $parents );

		$args = array(
			'post_type' => 'listing',
			'post_status' => 'publish',
			'posts_per_page' => $limit,
			'orderby' => $settings['orderby'],
			'order' => $settings['order'],
			'tax_query' => array(),
			'meta_query' => array(),
		);

		if ( empty( $custom_fields_as_categories ) ) {
			$args['tax_query'][0] = array(
				'taxonomy' => $taxonomy,
				'field' => 'slug',
				'operator' => 'IN',
			);
		}

		if ( isset( $settings['featured'] ) && $settings['featured'] == 'yes' ) {
			$args['meta_key'] = '_featured';
			$args['meta_value'] = 'on';
		}

		if ( $feature ) {
			array_push( $args['tax_query'], array(
				'taxonomy' => 'listing_feature',
				'field' => 'term_id',
				'terms' => $feature,
				'operator' => 'IN'
			) );
		}

		if ( $regions ) {
			array_push( $args['tax_query'], array(
				'taxonomy' => 'region',
				'field' => 'term_id',
				'terms' => $regions,
				'operator' => 'IN'
			) );
		}

		if ( ! class_exists( 'Listeo_Core_Template_Loader' ) ) {
			return;
		}

		ob_start();
		?>
		<div class="custom-listings-categories-carousel-container data-sticky-container" data-sticky-container>
			<div class="custom-listing-carousels elementor-widget-listeo-listings-carousel">
				<?php
				if ( empty( $custom_fields_as_categories ) ) {

					foreach ( $parents as $parent ) {
						if ( ! term_has_posts( $parent->slug, $settings['taxonomy'], $args ) ) {
							continue;
						}

						$list_items[ $parent->slug ] = [ 
							'title' => $this->get_alt_name( $parent ),
							'id' => $parent->term_id,
							'slug' => $parent->slug,
							'level' => 'parent',
						];

						$children_args = array(
							'taxonomy' => $settings['taxonomy'],
							'orderby' => 'name',
							'order' => 'ASC',
							'hide_empty' => true,
							'number' => 99,
							'include' => $settings[ $taxonomy . '_include' ],
							'exclude' => $settings[ $taxonomy . '_exclude' ],
							'parent' => $parent->term_id,
						);

						$children = get_terms( $children_args );

						$children = sort_categories_by_alt_name( $children );

						if ( ! is_wp_error( $children ) && count( $children ) > 0 ) { ?>
							<div id="<?php echo $parent->slug; ?>" class="listing-categories-carousel-parent">
								<h2 class="listing-categories-carousel-parent-title"><?php echo $this->get_alt_name( $parent ); ?></h2>
							</div>
							<?php

							foreach ( $children as $child ) {
								if ( ! term_has_posts( $child->slug, $settings['taxonomy'], $args ) ) {
									continue;
								}

								$list_items[ $parent->slug ]['children'][ $child->slug ] = [ 
									'title' => $this->get_alt_name( $child ),
									'id' => $child->term_id,
									'slug' => $child->slug,
									'level' => 'child',
								];

								$this->listing_posts_carousel_section_term( $child, $settings, $args );

								$grandchildren = get_terms( array(
									'taxonomy' => $settings['taxonomy'],
									'orderby' => 'name',
									'order' => 'ASC',
									'hide_empty' => true,
									'number' => 99,
									'include' => $settings[ $taxonomy . '_include' ],
									'exclude' => $settings[ $taxonomy . '_exclude' ],
									'parent' => $child->term_id,
								) );

								$grandchildren = sort_categories_by_alt_name( $grandchildren );

								if ( ! is_wp_error( $grandchildren ) && count( $grandchildren ) > 0 ) {
									foreach ( $grandchildren as $grandchild ) {
										if ( ! term_has_posts( $grandchild->slug, $settings['taxonomy'], $args ) ) {
											continue;
										}

										$list_items[ $parent->slug ]['children'][ $child->slug ]['grandchildren'][ $grandchild->slug ] = [ 
											'title' => $this->get_alt_name( $grandchild ),
											'id' => $grandchild->term_id,
											'slug' => $grandchild->slug,
											'level' => 'grandchild',
										];

										$this->listing_posts_carousel_section_term( $grandchild, $settings, $args );
									}
								}
							}
						} else {
							$this->listing_posts_carousel_section_term( $parent, $settings, $args );
						}

					}
				} else {
					$custom_fields_as_categories_value = custom_listeo_get_fields()[ $custom_fields_as_categories ]['options'];
					$custom_fields_as_categories_value = custom_sort_options( $custom_fields_as_categories_value );

					foreach ( $custom_fields_as_categories_value as $key => $value ) {
						$section_args = [];
						$section_args['value'] = $key;
						$section_args['type'] = $custom_fields_as_categories;
						$section_args['name'] = $value;

						$args['meta_query'][0] = array(
							'key' => $custom_fields_as_categories,
							'value' => $value,
							'compare' => 'LIKE'
						);

						$wp_query = new \WP_Query( $args );
						if ( ! $wp_query->have_posts() ) {
							continue;
						}

						$list_items[ $section_args['value'] ] = [ 
							'title' => $section_args['name'],
							'slug' => sanitize_title( $section_args['value'] ),
						];

						$this->listing_posts_carousel_section( $section_args, $settings, $wp_query );
					}
				} ?>
			</div>
			<div class="custom-list-container">
				<ul class="custom-taxonomy-list">
					<?php
					foreach ( $list_items as $list_item ) { ?>
						<li class="parent-listing-category">
							<?php if ( isset( $list_item['level'] ) ) {
								echo $this->create_list_item( $list_item );
							} else { ?>
								<a href="#<?php echo $list_item['slug']; ?>">
									<span class="parent-category-title"><?php echo $list_item['title']; ?></span>
								</a>
							<?php }
							if ( ! empty( $list_item['children'] ) ) {
								?>
								<div class="child-category-wrap">
									<ul class="child-listing-categories">
										<?php
										foreach ( $list_item['children'] as $child_item ) { ?>
											<li class="child-listing-category">
												<?php
												echo $this->create_list_item( $child_item );
												if ( ! empty( $child_item['grandchildren'] ) ) {
													?>
													<div class="grandchild-category-wrap">
														<ul class="grandchild-listing-categories">
															<?php
															foreach ( $child_item['grandchildren'] as $grandchild_item ) { ?>
																<li class="grandchild-listing-category">
																	<?php echo $this->create_list_item( $grandchild_item ); ?>
																</li>
															<?php } ?>
														</ul>
													</div>
												<?php } ?>
											</li>
										<?php } ?>
									</ul>
								</div>
							<?php } ?>
						</li>
					<?php } ?>
				</ul>
			</div>
			<button class="open-taxonomy-list-btn" type="button">
				<span class="open-taxonomy-list-btn-icon">
					<svg aria-hidden="true" class="e-font-icon-svg e-fas-list" viewBox="0 0 512 512"
						xmlns="http://www.w3.org/2000/svg">
						<path
							d="M80 368H16a16 16 0 0 0-16 16v64a16 16 0 0 0 16 16h64a16 16 0 0 0 16-16v-64a16 16 0 0 0-16-16zm0-320H16A16 16 0 0 0 0 64v64a16 16 0 0 0 16 16h64a16 16 0 0 0 16-16V64a16 16 0 0 0-16-16zm0 160H16a16 16 0 0 0-16 16v64a16 16 0 0 0 16 16h64a16 16 0 0 0 16-16v-64a16 16 0 0 0-16-16zm416 176H176a16 16 0 0 0-16 16v32a16 16 0 0 0 16 16h320a16 16 0 0 0 16-16v-32a16 16 0 0 0-16-16zm0-320H176a16 16 0 0 0-16 16v32a16 16 0 0 0 16 16h320a16 16 0 0 0 16-16V80a16 16 0 0 0-16-16zm0 160H176a16 16 0 0 0-16 16v32a16 16 0 0 0 16 16h320a16 16 0 0 0 16-16v-32a16 16 0 0 0-16-16z">
						</path>
					</svg>
				</span>
				<span class="open-taxonomy-list-btn-text">SELECT CATEGORY</span>
			</button>
		</div>
		<script src="https://cdnjs.cloudflare.com/ajax/libs/sticky-js/1.3.0/sticky.min.js"
			integrity="sha512-3z3zGiu0PabNyuTAAfznBJFpOg4owG9oQQasE5BwiiH5BBwrAjbfgIe0RCdtHJ0BQV1YF2Shgokbz2NziLnkuQ=="
			crossorigin="anonymous" referrerpolicy="no-referrer"></script>
		<script>
			new Sticky('.open-taxonomy-list-btn');
		</script>
		<?php
		echo ob_get_clean();
	}

	protected function create_list_item( $list_item ) {
		$output = '';
		$icon = get_term_meta( $list_item['id'], 'icon', true );
		$_icon_svg = get_term_meta( $list_item['id'], '_icon_svg', true );
		$_icon_svg_image = wp_get_attachment_image_src( $_icon_svg, 'medium' );
		if ( empty( $icon ) ) {
			$icon = 'fa fa-globe';
		}

		$output .= '<a href="#' . $list_item['slug'] . '">';

		if ( ! empty( $_icon_svg_image ) ) {
			$output .= '<i class="' . $list_item['level'] . '-category-icon listeo-svg-icon-box-grid">' . listeo_render_svg_icon( $_icon_svg ) . '</i>';
		} else {
			$check_if_im = substr( $icon, 0, 3 );
			$icon = $check_if_im == 'im ' ? $icon : 'fa ' . $icon;
			$output .= ' <i class="' . esc_attr( $icon ) . '"></i>';
		}

		$output .= '<span class="' . $list_item['level'] . '-category-title">' . $list_item['title'] . '</span></a>';

		return $output;
	}

	protected function listing_posts_carousel_section_term( $term, $settings, $args ) {

		$args['tax_query'][0]['terms'] = $term->slug;
		$section_args = [];
		$section_args['value'] = $term->slug;
		$section_args['type'] = 'tax-' . $settings['taxonomy'];
		$section_args['name'] = $this->get_alt_name( $term );


		$wp_query = new \WP_Query( $args );
		if ( $wp_query->have_posts() ) {
			$this->listing_posts_carousel_section( $section_args, $settings, $wp_query );
		}
	}

	protected function listing_posts_carousel_section( $section_args, $settings, $wp_query ) {
		$template_loader = new \Listeo_Core_Template_Loader;

		$custom_field_heading = $settings['custom_field_heading'];
		$custom_field = $settings['custom_field'];

		$custom_field_heading_2 = $settings['custom_field_heading_2'];
		$custom_field_2 = $settings['custom_field_2'];

		$feature = $settings['feature'];
		$regions = $settings['regions'];

		if ( ! empty( $settings['regions'] ) ) {
			$regions = implode( ',', $regions );

			$regions = get_terms( array( 'taxonomy' => 'region', 'fields' => 'slugs', 'hide_empty' => false, 'include' => $regions ) );

			$regions = implode( ',', $regions );
		} else {
			$regions = false;
		}

		if ( ! empty( $settings['feature'] ) ) {
			$feature = implode( ',', $feature );

			$feature = get_terms( array( 'taxonomy' => 'listing_feature', 'fields' => 'slugs', 'hide_empty' => false, 'include' => $feature ) );

			$feature = implode( ',', $feature );
		} else {
			$feature = false;
		}

		$search_sidebar_query = $settings['disable_search_sidebar'] === 'yes' ? 'hidden' : false;

		$taxonomy = $settings['taxonomy'];

		$button_url = get_listing_category_link( $taxonomy );

		$query_args = array( $section_args['type'] => $section_args['value'], 'region' => $regions, 'tax-listing_feature' => $feature, 'sidebar' => $search_sidebar_query );

		if ( $settings['link_to_online_services'] == 'yes' ) {
			$query_args['cats'] = 'online-services';
		}

		$button_url = add_query_arg( $query_args, $button_url );

		?>
		<div id="<?php echo sanitize_title( $section_args['value'] ); ?>" class="listing-categories-carousel-header">
			<h3 class="listing-categories-carousel-title"><?php echo $section_args['name']; ?></h3>
			<?php if ( ! empty( $settings['button_text'] ) ) { ?>
				<a class="listing-categories-carousel-button" href="<?php echo esc_url( $button_url ); ?>">
					<?php _e( $settings['button_text'], 'listeo_core' ); ?>
				</a>
			<?php } ?>
		</div>
		<div class="simple-slick-carousel dots-nav"
			data-slick='{"autoplay": <?php echo $settings['autoplay'] == 'yes' ? 'true' : 'false' ?>, "autoplaySpeed": <?php echo $settings['autoplayspeed']; ?>, "slidesToScroll": 1,"slidesToShow": 4,"responsive": [{"breakpoint": 1024,"settings": {"slidesToShow": 2}},{"breakpoint": 768,"settings": {"slidesToShow": 1}}]}'>
			<?php
			while ( $wp_query->have_posts() ) :
				$wp_query->the_post();

				$custom_field_value = '';
				if ( ! empty( $custom_field ) ) {
					$custom_field_value = get_post_meta( get_the_ID(), $custom_field, true );

					if ( ! empty( $custom_field_value ) ) {
						$field = custom_listeo_get_fields()[ $custom_field ];
						$field_type = $field['type'];

						$custom_field_value = $this->custom_field_type( $field_type, $custom_field_value, $custom_field );
					}
				}

				$custom_field_value_2 = '';
				if ( ! empty( $custom_field_2 ) ) {
					$custom_field_value_2 = get_post_meta( get_the_ID(), $custom_field_2, true );

					if ( ! empty( $custom_field_value_2 ) ) {
						$field = custom_listeo_get_fields()[ $custom_field_2 ];
						$field_type = $field['type'];

						$custom_field_value_2 = $this->custom_field_type( $field_type, $custom_field_value_2, $custom_field_2 );
					}
				}
				?>
				<div class="fw-carousel-item">
					<?php $template_loader->set_template_data( [ 'custom_field_heading' => $custom_field_heading, 'custom_field_value' => $custom_field_value, 'custom_field_heading_2' => $custom_field_heading_2, 'custom_field_value_2' => $custom_field_value_2, 'hide_reviews' => $settings['hide_reviews'] ] )->get_template_part( 'content-listing-grid' ); ?>
				</div>
			<?php endwhile; // end of the loop.
	
			wp_reset_postdata();
			wp_reset_query(); ?>
		</div>
	<?php }

	protected function custom_field_type( $type, $value, $selected_custom_field ) {
		switch ( $type ) {
			case 'datetime':
				$meta_value_date = explode( ' ', $value, 2 );

				$date_format = get_option( 'date_format' );

				$meta_value_ = \DateTime::createFromFormat( listeo_date_time_wp_format_php(), $meta_value_date[0] );

				if ( $meta_value_ && ! is_string( $meta_value_ ) ) {
					$meta_value_stamp = $meta_value_->getTimestamp();
					$meta_value = date_i18n( $date_format, $meta_value_stamp );
				} else {
					$meta_value = $meta_value_date[0];
				}

				if ( isset( $meta_value_date[1] ) ) {
					$time = str_replace( '-', '', $meta_value_date[1] );
					$meta_value .= esc_html__( ' at ', 'listeo_elementor' );
					$meta_value .= date_i18n( get_option( 'time_format' ), strtotime( $time ) );
				}
				$cfoutput = $meta_value;
				break;

			case 'textarea':
				$cfoutput = preg_replace( '/\s*[\r\n]+\s*/', ', ', wp_kses_post( $value ) );
				break;

			case 'checkbox':
				if ( $value ) {
					$cfoutput = '<i class="fas fa-check"></i>';
				} else {
					$cfoutput = '<i class="fal fa-times-circle"></i>';
				}
				break;

			case 'multicheck_split':
			case 'select_multiple':
			case 'select':
				$options = custom_listeo_get_fields()[ $selected_custom_field ]['options'];
				if ( $type == 'select_multiple' || $type == 'multicheck_split' ) {
					$value = get_post_meta( get_the_ID(), $selected_custom_field, false );
				}

				if ( isset( $options[ $value ] ) ) {
					$cfoutput = $options[ $value ];
				}

				break;
			case 'file':
				$cfoutput = '<a href="' . $value . '" /> ' . esc_html__( 'Download', 'listeo_elementor' ) . ' ' . wp_basename( $value ) . ' </a>';

				break;

			default:
				if ( filter_var( $value, FILTER_VALIDATE_URL ) !== false ) {
					$cfoutput = '<a href="' . esc_url( $value ) . '" target="_blank">' . esc_url( $value ) . '</a>';
				} else {
					$cfoutput = $value;
				}

				break;
		}

		return $cfoutput;
	}

	protected function get_alt_name( $term ) {
		$alt_name = get_term_meta( $term->term_id, 'alt_name', true );
		if ( empty( $alt_name ) ) {
			$alt_name = $term->name;
		}

		return $alt_name;
	}

	protected function get_terms( $taxonomy ) {
		$taxonomies = get_terms( array( 'taxonomy' => $taxonomy, 'hide_empty' => false ) );

		$options = [ '' => '' ];

		if ( ! empty( $taxonomies ) ) :
			foreach ( $taxonomies as $taxonomy ) {
				$options[ $taxonomy->term_id ] = $taxonomy->name;
			}
		endif;

		return $options;
	}

	protected function get_posts() {
		$posts = get_posts(
			array(
				'numberposts' => 199,
				'post_type' => 'listing',
				'suppress_filters' => true
			) );

		$options = [ '' => '' ];

		if ( ! empty( $posts ) ) :
			foreach ( $posts as $post ) {
				$options[ $post->ID ] = get_the_title( $post->ID );
			}
		endif;

		return $options;
	}

	protected function get_taxonomies() {
		$taxonomies = get_object_taxonomies( 'listing', 'objects' );

		$options = [ '' => '' ];

		foreach ( $taxonomies as $taxonomy ) {
			$options[ $taxonomy->name ] = $taxonomy->label;
		}

		return $options;
	}

}
