<?php

/**
 * Awesomesauce class.
 *
 * @category   Class
 * @package    ElementorAwesomesauce
 * @subpackage WordPress
 * <AUTHOR> <<EMAIL>>
 * @copyright  2020 <PERSON>
 * @license    https://opensource.org/licenses/GPL-3.0 GPL-3.0-only
 * @link       link(https://www.benmarshall.me/build-custom-elementor-widgets/,
 *             Build Custom Elementor Widgets)
 * @since      1.0.0
 * php version 7.3.9
 */

namespace ElementorListeo\Widgets;

use Elementor\Widget_Base;
use Elementor\Controls_Manager;
use Elementor\Utils;

if ( ! defined( 'ABSPATH' ) ) {
	// Exit if accessed directly.
	exit;
}

/**
 * Awesomesauce widget class.
 *
 * @since 1.0.0
 */
class SearchBarRegion extends Widget_Base {

	/**
	 * Retrieve the widget name.
	 *
	 * @since 1.0.0
	 *
	 * @access public
	 *
	 * @return string Widget name.
	 */
	public function get_name() {
		return 'listeo-search-bar-region';
	}

	/**
	 * Retrieve the widget title.
	 *
	 * @since 1.0.0
	 *
	 * @access public
	 *
	 * @return string Widget title.
	 */
	public function get_title() {
		return __( 'Search Bar Region', 'listeo_elementor' );
	}

	/**
	 * Retrieve the widget icon.
	 *
	 * @since 1.0.0
	 *
	 * @access public
	 *
	 * @return string Widget icon.
	 */
	public function get_icon() {
		return 'eicon-search';
	}

	/**
	 * Retrieve the list of categories the widget belongs to.
	 *
	 * Used to determine where to display the widget in the editor.
	 *
	 * Note that currently Elementor supports only one category.
	 * When multiple categories passed, Elementor uses the first one.
	 *
	 * @since 1.0.0
	 *
	 * @access public
	 *
	 * @return array Widget categories.
	 */
	public function get_categories() {
		return array( 'listeo' );
	}

	/**
	 * Register the widget controls.
	 *
	 * Adds different input fields to allow the user to change and customize the widget settings.
	 *
	 * @since 1.0.0
	 *
	 * @access protected
	 */
	protected function register_controls() {
		$this->start_controls_section(
			'section_content',
			array(
				'label' => __( 'Content', 'listeo_elementor' ),
			)
		);

		$this->add_control(
			'regions',
			[ 
				'label' => __( 'Search regions', 'listeo_elementor' ),
				'type' => Controls_Manager::SELECT2,
				'label_block' => true,
				'multiple' => true,
				'default' => [],
				'options' => $this->get_terms( 'region' ),
			]
		);

		$this->add_control(
			'taxonomy',
			[ 
				'label' => __( 'Category', 'listeo_elementor' ),
				'type' => Controls_Manager::SELECT,
				'label_block' => true,
				'default' => 'service_category',
				'options' => [ 
					'service_category' => __( 'Service', 'listeo_elementor' ),
					'rental_category' => __( 'Products', 'listeo_elementor' ),
					'event_category' => __( 'Events', 'listeo_elementor' ),
					'classifieds_category' => __( 'Resources', 'listeo_elementor' ),
				],
			]
		);

		$this->add_control(
			'show_only_hidden',
			[ 
				'label' => __( 'Show only hidden options', 'listeo_elementor' ),
				'type' => Controls_Manager::SWITCHER,
				'label_on' => __( 'Yes', 'listeo_elementor' ),
				'label_off' => __( 'No', 'listeo_elementor' ),
				'return_value' => 'yes',
				'default' => 'no',
			]
		);

		$this->end_controls_section();
	}

	/**
	 * Render the widget output on the frontend.
	 *
	 * Written in PHP and used to generate the final HTML.
	 *
	 * @since 1.0.0
	 *
	 * @access protected
	 */
	protected function render() {
		$settings = $this->get_settings_for_display();

		$regions = $settings['regions'];

		$taxonomy = $settings['taxonomy'];

		$button_url = get_listing_category_link( $taxonomy );

		$show_only_hidden = $settings['show_only_hidden'];

		$query_args = [];

		$button_url = add_query_arg( $query_args, $button_url );

		ob_start(); ?>

		<form action="<?php echo esc_url( $button_url ) ?>" id="listeo_core-search-form"
			class="custom-region-search-form dynamic ajax-search" method="GET">
			<div class="main-search-input">
				<div class="main-search-input-item select-taxonomy">
					<div id="listeo-search-form_tax-<?php echo $taxonomy ?>">
						<select data-live-search="true" id="tax-<?php echo $taxonomy ?>" name="tax-<?php echo $taxonomy ?>"
							class="selectpicker" data-size="10" title="What are you looking for?"
							data-live-search-placeholder="Please select a Category or begin typing here">
							<option value="0">What are you looking for?</option>
							<?php
							$term_args = array(
								'taxonomy' => $taxonomy,
								'hide_empty' => false,
							);

							if ( $show_only_hidden === 'yes' ) {
								$term_args['meta_query'] = array(
									array(
										'key' => 'hide_in_search',
										'value' => 'on',
										'compare' => '='
									)
								);
							} else {
								$term_args['meta_query'] = array(
									'relation' => 'OR',
									array(
										'key' => 'hide_in_search',
										'value' => 'on',
										'compare' => '!='
									),
									array(
										'key' => 'hide_in_search',
										'compare' => 'NOT EXISTS'
									)
								);
							}

							$terms = get_terms( $term_args );

							if ( ! empty( $terms ) && ! is_wp_error( $terms ) ) {
								$options = custom_listeo_core_get_options_array_hierarchical( $terms, 0, '', 0, 0, true );
								echo $options;
							}
							?>
						</select>
					</div>
				</div>

				<?php if ( ! empty( $regions ) ) : ?>
					<input type="hidden" name="region" value="<?php echo implode( ',', $regions ); ?>">
				<?php endif; ?>

				<?php if ( $show_only_hidden === 'yes' ) { ?>
					<input type="hidden" name="cats" value="online-services">
				<?php } ?>

				<button class="button">Search</button>
			</div>
		</form>

		<?php
		echo ob_get_clean();
	}

	protected function get_terms( $taxonomy ) {
		$taxonomies = get_terms( array( 'taxonomy' => $taxonomy, 'hide_empty' => false ) );

		$options = [ '' => '' ];

		if ( ! empty( $taxonomies ) ) :
			foreach ( $taxonomies as $taxonomy ) {
				$options[ $taxonomy->slug ] = $taxonomy->name;
			}
		endif;

		return $options;
	}
}
