//ts-check

document.addEventListener('DOMContentLoaded', function () {
  /* -------- Mobile Header Search -------- */
  document.querySelector('.mobile-search-btn').addEventListener('click', function () {
    document.querySelector('.header-search-container').classList.toggle('active');
    this.classList.toggle('active');
  });

  /* Fix Homepage Search */
  const homeSearchSelect = document.querySelector('.listeo-form-search_on_home_page select#tax-service_category');
  if (homeSearchSelect) {
    homeSearchSelect.addEventListener('change', function () {
      if (this.value !== '0') {
        this.parentElement.classList.add('selected');
      } else {
        this.parentElement.classList.remove('selected');
      }
    });
  }

  document.querySelectorAll(':not(.pricing-package-select) > .sign-in[href="#sign-in-dialog"]').forEach((button) => {
    button.addEventListener('click', function (e) {
      const inputPackage = document.querySelectorAll('#sign-in-dialog form#register input[name="package"], #sign-in-dialog form#login input[name="package"]');

      inputPackage.forEach((input) => {
        input.value = '';
      });
    });
  });
});
