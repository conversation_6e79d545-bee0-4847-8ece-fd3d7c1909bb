//ts-check

document.addEventListener('DOMContentLoaded', function () {
  /* -------- Remove extra from Single Checkbox -------- */
  const checkboxes = document.querySelectorAll('.single-checkbox');
  checkboxes.forEach((checkbox) => {
    const checkboxParent = checkbox.closest('#tax-listing_category-panel, .panel-dropdown');

    if (checkboxParent) {
      checkboxParent.before(checkbox);
      checkboxParent.remove();
    }
  });

  /* -------- Sort Alphabetically -------- */
  document.querySelectorAll('.sort-alphabetically').forEach((select) => {
    const items = Array.from(select.children); // Collect all input-label pairs
    const pairs = [];
    for (let i = 0; i < items.length; i += 2) {
      pairs.push({
        input: items[i],
        label: items[i + 1],
      });
    }

    // Sort pairs alphabetically by label text
    pairs.sort((a, b) => {
      const textA = a.label.textContent.trim().toLowerCase();
      const textB = b.label.textContent.trim().toLowerCase();
      return textA.localeCompare(textB);
    });

    // Clear the select and append sorted pairs
    select.innerHTML = '';
    pairs.forEach((pair) => {
      select.appendChild(pair.input);
      select.appendChild(pair.label);
    });
  });

  /* -------- Single Taxonomy Select -------- */
  document.querySelectorAll('.single-taxonomy-select').forEach((select) => {
    /* -------- Disable all options except the select option and change parent label -------- */
    const parentLabel = select.parentElement.previousElementSibling;
    const selectElemID = select.querySelector('select').id;

    const listItemsInterval = setInterval(() => {
      if (!select.querySelector('.dropdown-menu.inner a')) return;
      clearInterval(listItemsInterval);

      const listItems = select.querySelectorAll('.dropdown-menu.inner a');

      listItems.forEach((listItem, index) => {
        listItem.addEventListener('click', () => {
          parentLabel.innerHTML = listItem.querySelector('.option-text').textContent.trim();

          select.closest('.panel-dropdown').classList.remove('active');
          select.closest('.fs-inner-container.content').classList.remove('faded-out');
        });

        // Preselect option when all list items are loaded
        if (listItems.length === index + 1) {
          preselectOption();
        }
      });

      function preselectOption() {
        /* -------- Check and select relevant option based on query string -------- */
        const urlParams = new URLSearchParams(window.location.search);
        const selectQueryParam = urlParams.get(selectElemID);

        if (selectQueryParam) {
          const selectedOptionIndex = select.querySelector(`option[value="${selectQueryParam}"]`).index;
          const selectDropdownOption = select.querySelector(`.dropdown-menu li[data-original-index="${selectedOptionIndex}"] a`);

          selectDropdownOption.click();
        } else {
          /* -------- Check and select relevant category based on pathname -------- */
          const pathname = window.location.pathname;
          let pathParts = pathname.split('/');

          // Filter out empty strings
          pathParts = pathParts.filter(Boolean);

          if (pathParts.length > 1) {
            const serviceCategory = pathParts[1];
            const selectedOptionIndex = select.querySelector(`option[value="${serviceCategory}"]`).index;
            const selectDropdownOption = select.querySelector(`.dropdown-menu li[data-original-index="${selectedOptionIndex}"] a`);

            selectDropdownOption.click();

            // Update parent label
            // const parentLabel = select.parentElement.previousElementSibling;

            // if (!selectInput) return;
            // selectInput.checked = true;
            // parentLabel.innerHTML = selectInput.nextElementSibling.innerHTML;
            // selectInput.classList.add('selected');
          }
        }
      }
    }, 100);
  });

  /* -------- Single Select Search Form -------- */
  document.querySelectorAll('.single-select').forEach((select) => {
    /* -------- Disable all options except the select option and change parent label -------- */
    const parentLabel = select.parentElement.previousElementSibling;
    const parentLabelDefaultText = parentLabel.innerHTML;

    select.querySelectorAll('input').forEach((input) => {
      // Deselect all other options when one is selected
      input.addEventListener('change', (event) => {
        const isChecked = event.target.checked;
        parentLabel.innerHTML = isChecked ? event.target.nextElementSibling.innerHTML : parentLabelDefaultText;
        event.target.classList.toggle('selected', isChecked);

        if (isChecked) {
          select.querySelectorAll('input').forEach((input) => {
            if (input !== event.target) {
              input.checked = false;
              input.classList.remove('selected');
            }
          });

          select.closest('.panel-dropdown').classList.remove('active');
          select.closest('.fs-inner-container.content').classList.remove('faded-out');
        }
      });
    });

    /* -------- Check and select relevant option based on query string -------- */
    const urlParams = new URLSearchParams(window.location.search);
    const selectQueryParam = urlParams.get(select.id);

    if (selectQueryParam) {
      document.querySelector(`label[for="${selectQueryParam}-${select.id}"]`).click();
    }
  });

  /* -------- Move Icon to Parent -------- */
  const parents = document.querySelectorAll('.fs-container .panel-wrapper .panel-dropdown [class*="fa-"]');
  parents.forEach(function (parent) {
    const parentClassIcon = parent.className.match(/fa-[a-z-]+/)[0];
    parent.classList.remove(parentClassIcon);
    parent.closest('.panel-dropdown').classList.add(parentClassIcon);
  });

  /* -------- Check and select relevant region based on query string -------- */
  if (!document.querySelector('#tax-region')) return;
  const urlParams = new URLSearchParams(window.location.search);
  const regionQueryParams = decodeURIComponent(urlParams.get('region') ?? '').split(',');

  regionQueryParams.forEach((regionQueryParam) => {
    if (regionQueryParam) {
      const selectedRegion = document.querySelector(`input[id="${regionQueryParam}-tax-region"]`);

      selectedRegion.click();

      selectedRegion.checked = true;
    }
  });
});
