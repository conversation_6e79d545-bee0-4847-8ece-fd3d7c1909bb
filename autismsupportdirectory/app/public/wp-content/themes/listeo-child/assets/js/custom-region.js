document.addEventListener('DOMContentLoaded', function () {
  const listingsCategoriesCarousels = document.querySelectorAll('.elementor-widget-listeo-listings-categories-carousel');

  if (!listingsCategoriesCarousels.length) return;

  listingsCategoriesCarousels.forEach((listingsCategoriesCarousel) => {
    const openTaxonomyListBtn = listingsCategoriesCarousel.querySelector('.open-taxonomy-list-btn');
    const taxonomyListContainer = listingsCategoriesCarousel.querySelector('.custom-list-container');
    const headings = listingsCategoriesCarousel.querySelectorAll('.listing-categories-carousel-header');
    const menuItems = listingsCategoriesCarousel.querySelectorAll('.custom-taxonomy-list li');

    openTaxonomyListBtn.addEventListener('click', function () {
      taxonomyListContainer.classList.toggle('show');
    });

    menuItems.forEach((li) => {
      const link = li.querySelector('a');
      if (!link) return;
      link.addEventListener('click', function (e) {
        e.preventDefault();

        // 1) Extract ID from href
        const href = link.getAttribute('href'); // e.g. "#my-heading"
        const targetId = href.replace(/^#/, ''); // remove the '#' if present

        // 2) Check if there's an element with that ID
        const targetElement = document.getElementById(targetId);

        // Close the taxonomy list container when a link is clicked and no child list is present
        if (!li.querySelector('ul')) {
          taxonomyListContainer.classList.remove('show');
        }

        // Adjust for the fixed header height in desktop view
        const offset = window.innerWidth > 1024 ? 160 : 40;

        if (targetElement) {
          window.scrollBy({
            top: targetElement.getBoundingClientRect().top - offset,
            behavior: 'smooth',
          });
        }
      });
    });

    window.addEventListener('scroll', function () {
      let closestDistance = Infinity;
      let currentTerm = '';

      // Calculate which heading is closest to the top of the viewport
      headings.forEach((heading) => {
        const rect = heading.getBoundingClientRect();
        const distance = Math.abs(rect.top); // Distance from viewport top

        // Update closest heading if this one is closer
        if (distance < closestDistance) {
          closestDistance = distance;
          currentTerm = heading.id;
        }
      });

      // Remove .active from all sidebar items, then add to the matched item
      menuItems.forEach((li) => {
        li.classList.remove('active');

        // Compare the <a> href to the `currentTerm`
        const link = li.querySelector('a');
        if (!link) return;

        // Extract the term from the href
        const linkTerm = link.getAttribute('href')?.substring(1);

        if (linkTerm === currentTerm) {
          // Add .active to the matched parent item if it exists
          const parentLi = li.parentElement.closest('li.parent-listing-category');
          if (parentLi) {
            parentLi.classList.add('active');
          }

          // Add .active to the matched child item if it exists
          const childLi = li.parentElement.closest('li.child-listing-category');
          if (childLi) {
            childLi.classList.add('active');
          }

          // Add .active to the matched item
          li.classList.add('active');
        }
      });
    });
  });
});
