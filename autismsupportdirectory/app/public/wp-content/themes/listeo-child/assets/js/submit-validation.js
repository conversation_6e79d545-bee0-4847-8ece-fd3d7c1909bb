// @ts-check

document.addEventListener('DOMContentLoaded', function () {
  const form = document.getElementById('submit-listing-form');
  if (!form) return;

  const progressSteps = document.querySelectorAll('.progress-step');
  const progressLines = document.querySelectorAll('.progress-line');
  const progressText = document.querySelectorAll('.progress-step-text');

  // Multi-step functionality
  const steps = Array.from(form.querySelectorAll('.add-listing-section'));
  let currentStep = 0;

  // TODO: Add draft code
  // // Add save draft button
  // const stepButtons = document.querySelector('.step-buttons');
  // if (stepButtons) {
  //   const saveButton = document.createElement('button');
  //   saveButton.type = 'button';
  //   saveButton.className = 'button save-draft-btn';
  //   saveButton.innerHTML = 'Save Draft <i class="fa fa-save"></i>';
  //   saveButton.style.marginRight = '10px';
  //   stepButtons.prepend(saveButton);

  //   saveButton.addEventListener('click', saveDraft);
  // }

  // // Save draft functionality
  // function saveDraft() {
  //   const formData = new FormData(form);
  //   formData.append('action', 'save_listing_draft');
  //   formData.append('current_step', currentStep);
  //   formData.append('security', listeo_core_submit.nonce);

  //   // Show saving indicator
  //   const savingNotice = document.createElement('div');
  //   savingNotice.className = 'notification notice';
  //   savingNotice.innerHTML = '<p>Saving your draft...</p>';
  //   form.prepend(savingNotice);

  //   fetch(listeo_core_submit.ajax_url, {
  //     method: 'POST',
  //     body: formData,
  //   })
  //     .then((response) => response.json())
  //     .then((data) => {
  //       savingNotice.remove();

  //       const noticeClass = data.success ? 'success' : 'error';
  //       const notice = document.createElement('div');
  //       notice.className = `notification ${noticeClass}`;
  //       notice.innerHTML = `<p>${data.message}</p>`;
  //       form.prepend(notice);

  //       setTimeout(() => {
  //         notice.remove();
  //       }, 3000);
  //     })
  //     .catch((error) => {
  //       savingNotice.remove();

  //       const notice = document.createElement('div');
  //       notice.className = 'notification error';
  //       notice.innerHTML = '<p>Error saving draft. Please try again.</p>';
  //       form.prepend(notice);

  //       setTimeout(() => {
  //         notice.remove();
  //       }, 3000);
  //     });
  // }

  function updateProgress(stepIndex) {
    if (document.querySelector('.submit-page')?.classList.contains('type-service')) {
      stepIndex = stepIndex - 1; // If service listing then skip first step
    }

    if (stepIndex < 0) {
      document.querySelector('.progress-container').style.display = 'none';
    } else {
      document.querySelector('.progress-container').style.display = 'flex';
    }

    progressSteps.forEach((progressStep, index) => {
      if (index < stepIndex) {
        progressStep.classList.add('completed');
        progressStep.classList.remove('active');
        progressText[index].classList.remove('active');
      } else if (index === stepIndex) {
        progressStep.classList.add('active');
        progressStep.classList.remove('completed');
        progressText[index].classList.add('active');
      } else {
        progressStep.classList.remove('active', 'completed');
        progressText[index].classList.remove('active');
      }
    });

    progressLines.forEach((progressLine, index) => {
      if (index < stepIndex) {
        progressLine.classList.add('active');
      } else {
        progressLine.classList.remove('active');
      }
    });
  }

  const errorHeading = (element) => {
    const label = element.closest('div[class*="form-field-"]').querySelector('label');
    let content = '';
    for (let i = 0; i < label.childNodes.length; i++) {
      content = label.childNodes[i].textContent;

      if (content.replace(/[\n\t]/g, '').trim() !== '') {
        return content;
      }
    }

    return content;
  };

  // Step validation
  function validateStep(stepIndex) {
    const step = steps[stepIndex];
    const requiredFields = step.querySelectorAll('input[required], textarea.is-required, select[required], .checkboxes.is-required');

    // Scroll to step
    const stepTop = step.getBoundingClientRect().top + window.scrollY - 200;
    window.scrollTo({ top: stepTop, behavior: 'smooth' });

    // Clear previous errors
    step.querySelectorAll('.is-field-error').forEach((error) => error.classList.remove('is-field-error'));
    step.querySelectorAll('.notification.error.listing-manager-error').forEach((error) => error.remove());

    const errorList = [];

    requiredFields.forEach((field) => {
      // Reuse validation logic from original implementation

      let errorField = {};
      if (field.tagName === 'INPUT') {
        if (!field.value.trim()) {
          errorField = { field, msg: ' is a required field' };
        }
      } else if (field.tagName === 'TEXTAREA') {
        if (field.className.includes('wp-editor-area')) {
          field.value = tinymce.get(field.id).getContent().trim();

          if (field.className.includes('is-required') && field.value.length < 200) {
            errorField = { field, msg: ' must be at least 200 characters' };
          }
        } else if (field.className.includes('is-required') && field.value.length < 1) {
          errorField = { field, msg: ' is a required field' };
        }
      } else if (field.tagName === 'SELECT') {
        if (!field.value.trim() || field.value === '-1') {
          errorField = { field, msg: ' is a required field' };
        }
      } else if (field.classList.contains('checkboxes')) {
        const checkboxes = field.querySelectorAll('input[type="checkbox"]');
        let isChecked = false;
        checkboxes.forEach((input) => {
          if (input.checked) isChecked = true;
        });
        if (!isChecked) {
          errorField = { field, msg: ' is a required field' };
        }
      }

      if (errorField.field) {
        if (field.className.includes('custom-required-1')) {
          errorField.msg = 'In order to submit your Listing, please confirm your agreement to the Declaration by checking the box.';
          errorField.custom = true;
        }

        errorList.push(errorField);
      }
    });

    // Show errors within current step
    if (errorList.length > 0) {
      const errorElement = document.createElement('div');
      errorElement.className = 'notification error listing-manager-error';
      errorElement.innerHTML = errorList.map((error) => (error.custom ? '' : errorHeading(error.field)) + error.msg).join('<br /><br />');

      const headline = step.querySelector('.add-listing-headline');
      if (headline) {
        headline.after(errorElement);
      } else {
        step.prepend(errorElement);
      }
    }

    return errorList.length === 0;
  }

  // Step navigation buttons
  const backBtn = form.querySelector('.back-btn');
  backBtn?.addEventListener('click', () => {
    steps[currentStep].classList.remove('active');
    currentStep--;
    steps[currentStep].classList.add('active');
    updateProgress(currentStep);
    checkButtons();
  });

  const nextBtn = form.querySelector('.next-btn');
  nextBtn?.addEventListener('click', () => {
    if (validateStep(currentStep)) {
      steps[currentStep].classList.remove('active');
      currentStep++;
      steps[currentStep].classList.add('active');
      updateProgress(currentStep);
      checkButtons();
    }
  });

  const submitBtn = form.querySelector('.submit-btn');
  submitBtn?.addEventListener('click', (e) => {
    e.preventDefault();
    if (validateStep(currentStep)) {
      form.submit();

      // Show loading indicator
      const loader = document.createElement('div');
      loader.className = 'form-complete';
      loader.innerHTML += '<div class="loader-ajax-container"><div class="loader-ajax"></div><p>Please wait - uploading your Listing</p></div>';
      form.appendChild(loader);
    }
  });

  function checkButtons() {
    if (currentStep > 0) {
      backBtn.style.display = 'block';
    } else {
      backBtn.style.display = 'none';
    }

    if (currentStep === steps.length - 1) {
      nextBtn.style.display = 'none';
      submitBtn.style.display = 'block';
    } else {
      nextBtn.style.display = 'block';
      submitBtn.style.display = 'none';
    }
  }

  // Initialize progress
  updateProgress(currentStep);

  steps[0].classList.add('active');

  // Tip Alert
  document.querySelectorAll('.submit-page:not(.package-platinum) .label-service_category .tip, .submit-page:not(.package-platinum)  .label-tax-region .tip, .submit-page:not(.package-platinum)  .label-tax-service_category .tip').forEach((tip) => {
    tip.addEventListener('click', () => {
      alert(tip.textContent);
    });
  });
});
