/* ----------------- Start Document ----------------- */
(function ($) {
  'use strict';

  $(document).ready(function () {
    // Preserve form when not refreshing page.
    $(document).on('click', 'a', function () {
      // We're moving away to another page. Let's make sure the form persist.
      $('div.job_listings').each(function () {
        persist_form($(this));
      });
    });

    $(document).on('submit', 'form', function () {
      // We're moving away from current page from another form. Let's make sure the form persist.
      $('div.job_listings').each(function () {
        persist_form($(this));
      });
    });

    if ($('body.listeo-child-theme .fs-container section.search #listeo_core-search-form').hasClass('ajax-search')) {
      $('.fullwidth-filters').addClass('ajax-search');
    }

    // Add blur event handler for location search
    $(document).on('blur', '#location_search', function () {
      if (window.innerWidth > 1023) {
        var target = $('div#listeo-listings-container');
        target.triggerHandler('update_results', [1, false]);
      }
    });

    $('#listeo-listings-container').on('update_results', function (event, page, append, loading_previous) {
      var results = $('#listeo-listings-container');

      var filter = $('body.listeo-child-theme .fs-container section.search #listeo_core-search-form');
      var data = filter.serializeArray();
      var style = results.data('style');
      var grid_columns = results.data('grid_columns');
      var tax_region = results.data('region');
      var tax_category = results.data('category');
      var tax_service_category = results.data('service-category');
      var tax_rental_category = results.data('rental-category');
      var tax_event_category = results.data('event-category');
      var tax_classifieds_category = results.data('classifieds-category');
      var _listing_type = results.data('_listing_type');
      var show_hidden = results.data('show_hidden');

      var tax_feature = results.data('feature');
      var per_page = results.data('per_page');
      var custom_class = results.data('custom_class');
      var order = results.data('orderby');

      data.find((item) => item.name === 'action').value = 'listeo_get_listings_archive';
      data.push({ name: 'page', value: page });
      data.push({ name: 'style', value: style });
      data.push({ name: 'grid_columns', value: grid_columns });
      data.push({ name: 'per_page', value: per_page });
      data.push({ name: 'custom_class', value: custom_class });
      data.push({ name: 'order', value: order });
      if (_listing_type) data.push({ name: '_listing_type', value: _listing_type });
      if (show_hidden) data.push({ name: 'show_hidden', value: show_hidden });

      var mapBounds = filter.find('.map-bounds');
      if (mapBounds.length) {
        mapBounds.each(function () {
          data.push({
            name: $(this).attr('name'),
            value: $(this).val(),
          });
        });
      }

      var has_listing_category_search = false;
      var has_service_category_search = false;
      var has_rental_category_search = false;
      var has_event_category_search = false;
      var has_classifieds_category_search = false;
      var has_listing_feature_search = false;
      var has_region_search = false;

      $.each(data, function (i, v) {
        if (v.name.substring(0, 15) == 'tax-listing_cat') {
          if (v.value) {
            has_listing_category_search = true;
          }
        }
        if (v.name.substring(0, 15) == 'tax-listing_cat') {
          if (v.value) {
            has_listing_category_search = true;
          }
        }

        if (v.name.substring(0, 15) == 'tax-listing_fea') {
          if (v.value) {
            has_listing_feature_search = true;
          }
        }
        if (v.name.substring(0, 9) == 'tax-regio') {
          if (v.value) {
            has_region_search = true;
          }
        }
      });

      if (!has_region_search) {
        if (tax_region) {
          data.push({ name: 'tax-region', value: tax_region });
        }
      }
      if (!has_listing_category_search) {
        if (tax_category) {
          data.push({ name: 'tax-listing_category', value: tax_category });
        }
      }
      if (!has_listing_feature_search) {
        if (tax_feature) {
          data.push({ name: 'tax-listing_feature', value: tax_feature });
        }
      }
      if (!has_rental_category_search) {
        if (tax_rental_category) {
          data.push({
            name: 'tax-rental_category',
            value: tax_rental_category,
          });
        }
      }
      if (!has_event_category_search) {
        if (tax_event_category) {
          data.push({ name: 'tax-event_category', value: tax_event_category });
        }
      }
      if (!has_classifieds_category_search) {
        if (tax_classifieds_category) {
          data.push({
            name: 'tax-classifieds_category',
            value: tax_classifieds_category,
          });
        }
      }
      if (!has_service_category_search) {
        if (tax_service_category) {
          data.push({
            name: 'tax-service_category',
            value: tax_service_category,
          });
        }
      }

      $.ajax({
        type: 'post',
        dataType: 'json',
        url: listeo_core.ajax_url,
        data: data,
        beforeSend: function (xhr) {
          results.addClass('loading');
        },
        success: function (data) {
          $(results).html(data.html);
          $('div.pagination-container').html(data.pagination);
          $('.numerical-rating').numericalRating();
          $('.star-rating').starRating();
          if (listeo_core.map_provider == 'google') {
            var map = document.getElementById('map');
            if (typeof map != 'undefined' && map != null) {
              //	mainMap();
            }
          }
        },
      });

      // Change action to 'listeo_get_listings_map'
      data.find((item) => item.name === 'action').value = 'listeo_get_listings_map';

      $.ajax({
        type: 'post',
        dataType: 'json',
        url: listeo_core.ajax_url,
        data: data,
        success: function (data) {
          results.removeClass('loading');
          $('.filter-results-found span.results-found').text(data.found_results);
          $('.hidden-search-map-results').html(data.html);
          $('#listeo-listings-container').triggerHandler('update_results_success');
        },
      });
    });

    $(document)
      .on('change', '.sort-by-select .orderby, #listeo_core-search-form.ajax-search select, .ajax-search input:not(#location_search,#_price_range,.bootstrap-range-slider,.form-control)', function (e) {
        if (window.innerWidth > 1023) {
          var target = $('div#listeo-listings-container');
          target.triggerHandler('update_results', [1, false]);
        }
        //job_manager_store_state( target, 1 );
      })
      .on('keyup', function (e) {
        if (e.which === 13) {
          e.preventDefault();
          $(this).trigger('change');
        }
      });

    $('.bootstrap-range-slider').on('slideStop', function () {
      if (window.innerWidth > 1023) {
        var target = $('div#listeo-listings-container');
        target.triggerHandler('update_results', [1, false]);
      }
    });
    // $( '#listeo_core-search-form.ajax-search input.location_search' ).change( function() {
    // 	var target   = $('div#listeo-listings-container' );
    // 	target.triggerHandler( 'update_results', [ 1, false ] );
    // 	//job_manager_store_state( target, 1 );
    // } );

    if ($(' #listeo_core-search-form:not(.main-search-form)').length) {
      document.getElementById('listeo_core-search-form').onkeypress = function (e) {
        if (window.innerWidth > 1023) {
          var key = e.charCode || e.keyCode || 0;
          if (key == 13) {
            if ($('#location_search:focus').length) {
              return false;
            }
            var target = $('div#listeo-listings-container');
            target.triggerHandler('update_results', [1, false]);
            e.preventDefault();
          }
        }
      };
    }

    $(document).on('click', 'span.panel-disable,.slider-disable', function (e) {
      $('.location input#location_search').val('');

      if (window.innerWidth > 1023) {
        var results = $('#listeo-listings-container');
        results.triggerHandler('update_results', [1, false]);
      }
    });

    $(document).on('click', '.mobile-refine-btn', function (e) {
      var results = $('#listeo-listings-container');
      results.triggerHandler('update_results', [1, false]);
      document.querySelector('section.search').classList.toggle('show');
    });

    //$(document).on('click', 'div.pagination-container a', function(e) {
    $('div.pagination-container.ajax-search').on('click', 'a', function (e) {
      e.preventDefault();
      var results = $('#listeo-listings-container');
      var filter = $('#listeo_core-search-form');
      var page = $(this).parent().data('paged');

      if (page == 'next') {
        var page = $('.pagination li.current').data('paged') + 1;
      }
      if (page == 'prev') {
        var page = $('.pagination li.current').data('paged') - 1;
      }
      results.triggerHandler('update_results', [page, false]);

      $('body, html').animate(
        {
          scrollTop: $('.fs-inner-container .search,#titlebar, .ajax-search,#map-container').offset().top,
        },
        600
      );

      return false;
    });

    var init_layout = $('#listeo-listings-container').data('style');

    if (init_layout == 'list') {
      $('.layout-switcher a').removeClass('active');
      $('.layout-switcher a.list').addClass('active');
    } else {
      $('.layout-switcher a:not(.list)').addClass('active');
    }

    $('.tax-listing_category #tax-listing_category').on('change', function (e) {
      var label = $(this).find(':selected').html();
      $('.page-title').html(label);
    });

    $('.layout-switcher').on('click', 'a', function (e) {
      e.preventDefault();
      $('.layout-switcher a').removeClass('active');
      $(this).addClass('active');
      var layout = $(this).data('layout');
      var results = $('#listeo-listings-container');
      results.data('style', layout);
      var page = 1;
      results.triggerHandler('update_results', [page, false]);
    });
    // ------------------ End Document ------------------ //
  });
})(this.jQuery);
/**/
