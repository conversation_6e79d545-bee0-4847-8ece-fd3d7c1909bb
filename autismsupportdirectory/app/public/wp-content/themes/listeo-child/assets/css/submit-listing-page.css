.is-field-error {
    border: 1px solid red;
    border-radius: 4px;
    box-shadow: 0px 0px 5px 0 #ff2727;
}

#submit-listing-form .form-field-_geolocation_lat-container,
#submit-listing-form .form-field-_geolocation_long-container {
    display: none;
}

#submit-listing-form .checkboxes.in-row {
    display: flex;
    flex-direction: column;
    margin-bottom: 20px !important;
}

#submit-listing-form .add-listing-headline h3 {
    font-weight: bold;
    color: #000;
}

#submit-listing-form .add-listing-section>div[class*="form-field-"]:not(:last-child) {
    margin-bottom: 18px;
}

.saved-drafts-list {
    margin: 15px 0;
    padding: 0;
    list-style: none;
}

.saved-drafts-list li {
    margin-bottom: 10px;
    padding: 10px;
    background: #f9f9f9;
    border-radius: 4px;
    border-left: 3px solid #66676b;
}

.saved-drafts-list a {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #444;
    font-weight: 600;
}

.saved-drafts-list .saved-date {
    font-size: 13px;
    color: #888;
    font-weight: normal;
}

.save-draft-btn {
    background-color: #e8e8e8 !important;
    color: #666 !important;
}

.save-draft-btn:hover {
    background-color: #d8d8d8 !important;
}

.form-step {
    display: none;
    animation: fadeIn 0.3s ease-out;
}

.form-step.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.step-buttons {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.step-buttons .fa-arrow-circle-right {
    padding-left: 4px;
    padding-right: 0;
}

.step-buttons button.button {
    min-width: 130px;
}

.add-listing-section .select2-container--default {
    width: 100% !important;
}

.submit-page {
    width: 800px;
    max-width: 100%;
    margin: 0 auto;
}

#submit-listing-form .add-listing-section>div[class*="form-field-"] label b {
    color: #000;
    font-size: 110%;
}

#submit-listing-form .service_category-child_checkbox {
    margin-left: 26px;
}

#submit-listing-form .service_category-parent_checkbox {
    font-size: 16.5px;
    color: #000;
    padding-left: 0;
    cursor: none;
    pointer-events: none;
}

#submit-listing-form .service_category-parent_checkbox::before {
    display: none;
}

.add-listing-section {
    margin-right: 0 !important;
    margin-left: 0 !important;
}

form#submit-listing-form {
    position: relative;
}

.form-complete {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #ffffffa8;
    z-index: 1000;
}

.form-complete .loader-ajax-container {
    opacity: 1;
    visibility: visible;
    display: flex;
    justify-content: center;
}

.form-complete .loader-ajax-container p {
    white-space: nowrap;
    margin-top: 60px;
    font-weight: 600;
    font-size: 14px;
}

.submit-page:not(.package-platinum) .label-service_category .tip,
.submit-page:not(.package-platinum) .label-tax-region .tip,
.submit-page:not(.package-platinum) .label-tax-service_category .tip {
    cursor: pointer !important;
}

.submit-page:not(.package-platinum) .label-service_category .tip .tip-content,
.submit-page:not(.package-platinum) .label-tax-region .tip .tip-content,
.submit-page:not(.package-platinum) .label-tax-service_category .tip .tip-content {
    display: none;
}

button[type="submit"] {
    margin-bottom: 40px;
}

.step-buttons button.button {
    margin-bottom: 40px;
}

.add-listing-dashboard-template #titlebar {
    display: none;
}

.add-listing-dashboard-template #header-container {
    margin-bottom: 60px;
}

i.tip::after {
    content: "\f129";
}

.label-_gallery {
    display: block !important;
}