.single-listing .share-listing {
  font-size: 15px;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  border-radius: 50px;
  background-color: transparent;
  border: 1px solid #4dd6b8;
  padding: 9px 30px;
  text-align: center;
  line-height: 26px;
  font-weight: 500;
  color: #4dd6b8;
  margin-left: auto;
  margin-right: auto;
  display: table;
  margin-bottom: 30px;
  margin-top: -15px;
}

.single-listing .share-listing:hover,
.single-listing .share-listing:active {
  background: #4dd6b8;
  color: #fff;
}

.single-listing .elementor-widget-listeo-listing-title .listing-logo {
  padding: 0;
}

.elementor-widget-listeo-listing-title .listing-logo img {
  max-height: 100%;
  object-fit: contain;
}

.single-listing .elementor-widget-listeo-listing-single-navigation li:not([class]) {
  display: none;
}

.single-listing .elementor-widget-listeo-listing-taxonomy-checkboxes-2 a {
  color: #707070;
}

.single-listing .elementor-widget-listeo-listing-taxonomy-checkboxes-2 a:hover,
.single-listing .elementor-widget-listeo-listing-taxonomy-checkboxes-2 a:focus {
  color: #4dd6b8;
}

.single-listing .elementor-widget-listeo-listing-taxonomy-checkboxes .cls-1,
.single-listing .elementor-widget-listeo-listing-taxonomy-checkboxes .cls-2,
.single-listing .elementor-widget-listeo-listing-taxonomy-checkboxes path[stroke],
.single-listing .elementor-widget-listeo-listing-taxonomy-checkboxes circle[stroke] {
  stroke: #4dd6b8;
  fill: transparent !important;
  transition: 0.3s;
}

.single-listing .elementor-widget-listeo-listing-taxonomy-checkboxes svg[stroke] {
  stroke: #4dd6b8;
}

.single-listing .elementor-widget-listeo-listing-taxonomy-checkboxes .category-small-box svg[stroke] {
  stroke: #fff;
}

.single-listing .elementor-widget-listeo-listing-taxonomy-checkboxes svg[stroke]>path {
  fill: transparent !important;
}

.elementor-widget-listeo-listing-related .listing-small-badges-container,
.elementor-widget-listeo-listing-related .listing-ndis-container {
  display: none;
}

.elementor-widget-listeo-listing-related .listing-item-container {
  background: #F9F9F9;
}

.elementor-widget-listeo-listing-related .listing-item-content {
  position: static;
  padding-top: 15px;
}

.elementor-widget-listeo-listing-related .listing-item {
  height: auto;
  background: none;
}

.elementor-widget-listeo-listing-related .listing-item::before {
  display: none;
}

.elementor-widget-listeo-listing-related .listing-item-content h3,
.elementor-widget-listeo-listing-related .listing-item-content span {
  color: #000;
}

.elementor-widget-listeo-listing-related .listing-item-content h3 {
  margin-bottom: 10px;
  font-size: 17px;
  line-height: 1.3;
}

.elementor-widget-listeo-listing-related .listing-item-content span {
  font-size: 14px;
  line-height: 1.4;
}

.elementor-widget-listeo-listing-related .listing-item-tags,
.elementor-widget-listeo-listing-related .listing-item-content .region {
  display: none;
}

.elementor-widget-listeo-listing-related .listing-item-container .star-rating {
  padding-top: 5px;
}

.elementor-widget-listeo-listing-related .listing-item img {
  object-fit: contain;
  background: #fff;
  aspect-ratio: 1.5 / 1;
}

.single-listing .leaflet-container .leaflet-popup-content {
  padding: 6px 12px;
  text-align: center;
  font-size: 14px;
  width: max-content !important;
}

.single-listing .leaflet-container a.leaflet-popup-close-button {
  display: none;
}

.elementor-widget-listeo-listing-custom-field li::before,
.elementor-widget-listeo-listing-taxonomy-checkboxes-2 li::before {
  content: "\f00c";
  font-family: 'FontAwesome';
  margin-left: -5px;
  margin-right: 8px;
  background: rgba(77, 214, 184, 0.08);
  border-radius: 50%;
  font-size: 11px;
  color: #4dd6b8;
  line-height: 0;
  width: 20px;
  height: 20px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

.elementor-widget-listeo-listing-custom-field li,
.elementor-widget-listeo-listing-taxonomy-checkboxes-2 li {
  list-style: none;
}

.elementor-widget-listeo-listing-custom-field i.tip::after {
  content: "\f129";
}

.elementor-widget-listeo-listing-taxonomy-checkboxes .listing-features.checkboxes a {
  font-size: 14px;
}

.elementor-widget-listeo-listing-title .listing-logo {
  width: 160px;
  height: 160px;
}

.elementor-widget-listeo-listing-title .listing-titlebar-title {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

.custom-gallery-justified {
  display: flex;
  flex-wrap: wrap;
}

/* .custom-gallery-justified::after {
  content: '';
  flex-grow: *********;
} */

.custom-gallery-justified-img-container {
  margin: 2px;
  position: relative;
}

.custom-gallery-justified-img-filler {
  display: block;
}

.custom-gallery-justified-img {
  position: absolute;
  top: 0;
  width: 100%;
  vertical-align: bottom;
}

.single-listing .elementor-widget-theme-post-content h1 {
  margin-top: 40px;
  margin-bottom: 32px;
  line-height: 140%;
  font-weight: 500;
}

.single-listing .elementor-widget-theme-post-content h2 {
  margin-top: 32px;
  margin-bottom: 24px;
  line-height: 140%;
  font-weight: 500;
}

.single-listing .elementor-widget-theme-post-content h2 {
  margin-top: 32px;
  margin-bottom: 24px;
  line-height: 140%;
  font-weight: 500;
}

.single-listing .elementor-widget-theme-post-content h3 {
  margin-top: 24px;
  margin-bottom: 16px;
  line-height: 140%;
  font-weight: 500;
}

.single-listing .elementor-widget-theme-post-content h4 {
  margin-top: 20px;
  margin-bottom: 12px;
  line-height: 140%;
  font-weight: 500;
}

.single-listing .dialog-type-lightbox {
  display: none !important;
}

.mfp-gallery .mfp-arrow {
  background: #4dd6b8 !important;
}

#titlebar.listing-titlebar .star-rating .rating-counter .star-rating-reviews {
  font-weight: 500;
  font-size: 14px;
  color: #999;
}

#titlebar.listing-titlebar .star-rating .rating-counter .star-rating-reviews strong {
  transition: 0.1s;
  color: #444;
}

img.feature-image {
  width: 70px;
}

@media (max-width: 767px) {
  #titlebar.listing-titlebar {
    align-items: baseline;
  }

  #titlebar.listing-titlebar {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding-top: 20px;
  }

  .elementor-widget-listeo-listing-title .listing-logo {
    width: 70%;
  }

  .google-reviews-summary {
    flex-wrap: wrap;
    row-gap: 30px;
  }

  .google-reviews-read-more:not(.google-reviews-read-more.bottom) {
    justify-content: center;
    display: flex;
  }
}