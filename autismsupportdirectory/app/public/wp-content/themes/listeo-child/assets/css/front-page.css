.home-grid-1 a.category-small-box.slick-slide {
	height: auto;
	padding-left: 12px;
	padding-right: 12px;
}

.home-grid-1 .slick-track {
	display: flex;
}

.home-grid-1 .category-small-box h4 {
	font-size: 12px;
	line-height: 1.5;
}

.home-grid-1 .general-carousel .slider-controls-container {
	bottom: -40px;
}

.simple-slick-carousel .fw-carousel-item .listing-item-content {
	position: static;
	padding-top: 20px;
}

.simple-slick-carousel .fw-carousel-item .listing-item {
	background: none;
	flex: 1;
	display: flex;
	flex-direction: column;
}

.simple-slick-carousel .fw-carousel-item .listing-item:before {
	display: none;
}

.simple-slick-carousel .slick-slide {
	height: auto;
}

.simple-slick-carousel .slick-track {
	display: flex;
}

.elementor .listing-item img {
	object-fit: contain;
	aspect-ratio: 1/1;
	height: auto;
}

@media (max-width: 767px) {
	.elementor .listing-item img {
		aspect-ratio: 1.5/1;
	}
}

.simple-slick-carousel .fw-carousel-item a.listing-item-container {
	background: #ffffff;
	display: flex;
	flex-direction: column;
	margin-bottom: 0;
}

.simple-slick-carousel .fw-carousel-item .listing-item .listing-item-content h3,
.simple-slick-carousel .fw-carousel-item .listing-item .listing-item-content span {
	color: #000;
}

.simple-slick-carousel .fw-carousel-item .listing-item .listing-item-content h3 {
	margin-bottom: 10px;
	font-size: 17px;
	line-height: 1.3;
}

.simple-slick-carousel .fw-carousel-item .listing-item .listing-item-content>span {
	font-size: 14px;
	line-height: 1.4;
}

.home-blog .row {
	display: grid;
	grid-template-columns: 1fr 1fr 1fr 1fr;
}

.home-blog .row:before,
.home-blog .row:after {
	display: none;
}

.home-blog .row .col-md-4 {
	width: 100%;
}

.home-blog .blog-compact-item {
	height: 380px;
}

.home-blog .blog-compact-item-content h3 {
	font-size: 17px;
	line-height: 1.4;
}

.home-blog .blog-compact-item-content p {
	font-size: 14px;
	line-height: 1.6;
}

.listing-item-content {
	padding-left: 25px;
	padding-right: 85px;
}

.home .listing-item-content .region {
	margin-bottom: 3px;
	font-weight: bold;
	font-size: 13px !important;
}

.home .listing-item-container .region {
	display: none;
}

.home .elementor-widget-listeo-taxonomy-grid .cls-1,
.home .elementor-widget-listeo-taxonomy-grid .cls-2,
.home .elementor-widget-listeo-taxonomy-grid path[stroke],
.home .elementor-widget-listeo-taxonomy-grid circle[stroke] {
	stroke: #4dd6b8;
	fill: transparent !important;
	transition: 0;
}

.home .elementor-widget-listeo-taxonomy-grid .category-small-box:hover g,
.home .elementor-widget-listeo-taxonomy-grid .category-small-box:hover circle,
.home .elementor-widget-listeo-taxonomy-grid .category-small-box:hover rect,
.home .elementor-widget-listeo-taxonomy-grid .category-small-box:hover path {
	fill: #fff;
}

.home .elementor-widget-listeo-taxonomy-grid .category-small-box:hover .cls-1,
.home .elementor-widget-listeo-taxonomy-grid .category-small-box:hover .cls-2,
.home .elementor-widget-listeo-taxonomy-grid .category-small-box:hover path[stroke],
.home .elementor-widget-listeo-taxonomy-grid .category-small-box:hover circle[stroke] {
	stroke: #fff;
}

.home .elementor-widget-listeo-taxonomy-grid svg[stroke] {
	stroke: #4dd6b8;
	transition: 0;
}

.home .elementor-widget-listeo-taxonomy-grid .category-small-box:hover svg[stroke] {
	stroke: #fff;
}

.home .elementor-widget-listeo-taxonomy-grid svg[stroke]>path {
	fill: transparent !important;
}

.home .listing-item-container .listing-item-tags {
	overflow: hidden;
	max-height: 105px;
	transition: max-height 0.5s cubic-bezier(0, 1, 0, 1);
	position: relative;
	margin-right: 0;
	margin-bottom: 0;
	margin-top: 12px;
	display: flex;
}

.home .listing-item-container:hover .listing-item-tags,
.home .listing-item-container:focus .listing-item-tags {
	transition: max-height 1s ease-in-out;
	max-height: 1000px;
}

.home .listing-item-container .listing-item-tags::before {
	content: '';
	position: absolute;
	left: 0;
	top: 78px;
	width: 100%;
	height: 30px;
	background: linear-gradient(#ffffff00 0%, #ffffff 100%);
	transition: .5s all ease-in-out
}

.home .listing-item-container:hover .listing-item-tags::before,
.home .listing-item-container:focus .listing-item-tags::before {
	top: 100%;
	transition: .1s all ease-in-out
}

.drilldown-menu .menu-levels .menu-level[data-level="1"] input.menu-search {
	display: none;
}

/* Homepage Search */

.listeo-form-search_on_home_page #location_search {
	color: #000;
}

.listeo-form-search_on_home_page .bootstrap-select.selected button.dropdown-toggle span.option-text {
	color: #000;
}

.listeo-form-search_on_home_page .bootstrap-select button.dropdown-toggle {
	font-size: 16px;
}

.listeo-form-search_on_home_page .listeo-svg-icon-box-grid rect#_Transparent_Rectangle_ {
	display: none;
}

/* End Homepage Search */

@media (max-width:1023px) {
	.main-search-container.plain-color .main-search-inner {
		display: flex;
		height: 100%;
	}

	.main-search-container.plain-color .main-search-inner .row,
	.main-search-container.plain-color .main-search-inner .row .col-md-12 {
		height: 100%;
	}

	.main-search-container.plain-color .main-search-inner .row .col-md-12 {
		justify-content: space-between;
		display: flex;
		flex-direction: column;
	}
}

@media (max-width: 1024px) {
	.home-blog .row {
		display: grid;
		grid-template-columns: 1fr 1fr;
	}
}

@media (max-width: 767px) {
	.home-grid-1 .category-small-box {
		margin: 20px 0 0 12px;
	}

	.main-search-container.plain-color {
		height: 70vh;
		background-position: 67% 100% !important;
	}

	.home-blog .row {
		display: grid;
		grid-template-columns: 1fr;
	}

	.home-blog .row>.col-md-4:nth-child(n + 4) {
		display: none;
	}
}