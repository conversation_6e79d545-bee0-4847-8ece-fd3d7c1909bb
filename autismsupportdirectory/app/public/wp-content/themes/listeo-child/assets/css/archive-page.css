.listing-item-container.list-layout .listing-item-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.listing-item-container.list-layout .listing-item-image:before {
  display: none;
}

.listing-item-container.list-layout .listing-item img {
  object-fit: contain;
}

.listings-container.list-layout .listing-item-tags {
  margin-right: 0;
}

body.listeo-child-theme .fs-container section.search {
  margin-left: 30px;
  padding: 20px 16px;
  width: 310px;
}

body.listeo-child-theme span.panel-disable {
  position: absolute;
  top: 10px;
  right: 15px;
}

body.listeo-child-theme section.listings-container {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
}

body.listeo-child-theme .fs-container .panel-wrapper {
  float: none;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

body.listeo-child-theme section.listings-container .row.fs-switcher {
  width: 100%;
}

body.listeo-child-theme div[id*='listeo-search-form_tax'] {
  width: 100%;
}

body.listeo-child-theme .fs-container .panel-wrapper .panel-dropdown {
  position: relative;
}

body.listeo-child-theme .fs-container .panel-wrapper .panel-dropdown>a {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 15px;
  border: 1px solid #dbdbdb;
  padding: 10px 20px;
  text-decoration: none;
  color: #666;
  background: none;
}

body.listeo-child-theme .fs-container .panel-wrapper .panel-dropdown .panel-dropdown-content {
  width: 100%;
  top: 100%;
  left: 0;
  border-radius: 20px;
}

body.listeo-child-theme .fs-container .row.fs-listings {
  flex: 1;
  padding-left: 5px;
  padding-right: 15px;
}

body.listeo-child-theme .fs-container .search-title {
  font-size: 19px;
  margin-top: 0;
  margin-bottom: 15px;
}

body.listeo-child-theme .fs-inner-container.content.faded-out .listings-containert {
  opacity: 1;
}

body.listeo-child-theme .listing-item-container.list-layout .listing-item-inner h3 {
  font-size: 18px;
  line-height: 22px;
  margin-bottom: 2px;
}

body.listeo-child-theme .listing-item-container.list-layout .listing-item-inner span {
  font-size: 14px;
  line-height: 18px;
}

body.listeo-child-theme #listeo_core-search-form .panel-dropdown[class*='fa-']::before {
  font-family: 'FontAwesome';
  color: #4dd6b8;
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
}

body.listeo-child-theme #listeo_core-search-form .panel-dropdown[class*='fa-']>a {
  padding-left: 48px;
}

body.listeo-child-theme .fs-container .panel-wrapper .panel-dropdown>a::after {
  color: #4dd6b8;
  margin-left: auto;
}

body.listeo-child-theme #footer {
  position: relative;
  z-index: 10;
}

body.listeo-child-theme #listeo_core-search-form .panel-dropdown>a.active .original-label,
body.listeo-child-theme #listeo_core-search-form .panel-dropdown>a:not(.active) .selected-label {
  display: none;
}

.listing-item-container.list-layout .listing-item:hover {
  text-decoration: none;
}

.single-select {
  padding-left: 0;
  padding-right: 0;
}

.single-select label {
  padding-left: 0;
  margin-bottom: 14px !important;
  transition: color .2s ease;
}

.single-select label::before {
  display: none;
}

.single-select label:hover {
  color: #4dd6b8;
}

.single-select input.selected+label {
  color: #4dd6b8;
}

.marker-container {
  margin-bottom: -10px;
}

.marker-container .marker-arrow {
  display: none;
}

.marker-container .front.face::after,
.marker-container .back.face::after {
  display: none;
}

.marker-container .front.face {
  background: url(/wp-content/themes/listeo-child/assets/img/map-logo.png);
}

.marker-container .back.face {
  background: url(/wp-content/themes/listeo-child/assets/img/map-logo-selected.png);
}

.marker-container .front.face,
.marker-container .back.face {
  background-size: contain !important;
  border: none;
}

.marker-container .front.face i,
.marker-container .back.face i {
  display: none;
}

.panel-wrapper .drilldown-menu .menu-toggle {
  font-weight: 600;
  font-size: 15px;
  border-radius: 50px;
  transition: all 0.3s;
  width: 100%;
  display: flex;
  align-items: center;
  gap: 15px;
  border: 1px solid #dbdbdb;
  padding: 10px 20px;
  text-decoration: none;
  color: #666;
  background: none !important;
}

.drilldown-menu {
  display: block;
}

.drilldown-menu .menu-panel {
  border-radius: 24px !important;
  padding: 20px 24px;
}

.drilldown-menu .menu-panel .menu-level {
  padding: 0;
}

.hidden-search-map-results {
  display: none;
}

.button.border.mobile-refine-btn {
  margin-top: 20px;
  display: none;
}

#listeo_core-search-form .location #autocomplete-container .pac-container {
  top: 100% !important;
  left: 0 !important;
}

/* Service Category Selection */

[id^="listeo-search-form_tax-"][id$="_category"] button.dropdown-toggle {
  display: none;
}

[id^="tax-"][id$="_category-panel"].active [id^="listeo-search-form_tax-"][id$="_category"] .dropdown-menu {
  opacity: 1;
  pointer-events: all;
  visibility: visible;
  position: static;
  border: none;
  box-shadow: none;
  padding-left: 0;
  padding-right: 0;
}

[id^="tax-"][id$="_category-panel"].active [id^="listeo-search-form_tax-"][id$="_category"] .dropdown-menu.inner {
  overflow-y: scroll;
}

[id^="tax-"][id$="_category-panel"] .panel-dropdown-content {
  padding-left: 0;
  padding-right: 0;
  overflow: hidden;
}

[id^="tax-"][id$="_category-panel"] [id^="listeo-search-form_tax-"][id$="_category"] .dropdown-menu a {
  color: #888;
  background: transparent;
  padding-right: 24px;
}

[id^="tax-"][id$="_category-panel"].active [id^="listeo-search-form_tax-"][id$="_category"] .dropdown-menu li:first-child span.text {
  font-size: 0;
}

[id^="tax-"][id$="_category-panel"].active [id^="listeo-search-form_tax-"][id$="_category"] .dropdown-menu li:first-child span.text::before {
  content: 'All Categories';
  font-size: 15px;
}

[id^="tax-"][id$="_category-panel"] .listeo-svg-icon-box-grid rect#_Transparent_Rectangle_ {
  display: none;
}

.fs-inner-container.content.no-map {
  width: 100%;
}

.listing-item-container.list-layout .listing-item-image {
  max-height: 200px;
}

.fs-inner-container.content.no-map .row.fs-listings {
  max-width: 720px;
}

/* End Service Category Selection */

.leaflet-listing-content .not-rated {
  display: none;
}

.online-services .with-forms.split-top-inputs {
  display: none;
}

.online-services div#tax-region-panel {
  display: none;
}

.online-services #listeo-search-form_tax-service_category ul.dropdown-menu.inner li[data-original-index="1"] {
  display: none;
}

body.listeo-child-theme .listing-item-container.list-layout .listing-item-inner span.custom-field {
  font-size: 12px;
  line-height: 1.5;
  color: #000;
  display: block;
}

@media (min-width: 768px) {
  .listing-item-container.list-layout .listing-item {
    display: flex;
  }

  .listing-item-container.list-layout span.like-icon {
    transform: none;
    bottom: 25px;
  }

  body.listeo-child-theme .listing-item-container.list-layout .listing-item-inner {
    padding: 15px 90px 15px 25px;
    position: static;
    transform: none;
  }

  body.listeo-child-theme .listing-item-container.list-layout .listing-ndis-container {
    right: 15px;
    top: 25px;
  }

  body.listeo-child-theme .listing-item-container.list-layout span.like-icon {
    right: 25px;
  }

  body.listeo-child-theme .listing-item-container.list-layout .star-rating {
    padding: 0;
    display: flex;
    align-items: center;
  }

  body.listeo-child-theme .listing-item-container.list-layout .star-rating .rating-counter {
    order: 1;
    position: static;
    font-size: 13px;
  }

  body.listeo-child-theme .listings-container.list-layout .listing-item-tags {
    gap: 5px;
    margin-top: 8px;
    margin-right: -90px;
    max-height: 90px;
    overflow: hidden;
    margin-bottom: 5px;
    align-items: flex-start;
    position: relative;
  }

  .listing-item-container.list-layout .listing-item {
    min-height: 220px;
    height: auto;
  }

  body.listeo-child-theme .listings-container.list-layout .listing-item .listing-item-tags::before {
    content: '';
    position: absolute;
    left: 0;
    top: 65px;
    width: 100%;
    height: 30px;
    background: linear-gradient(#ffffff00 0%, #ffffff 100%);
    transition: .5s all ease-in-out;
    z-index: 11111;
  }

  body.listeo-child-theme .listings-container.list-layout .listing-item:hover .listing-item-tags::before,
  body.listeo-child-theme .listings-container.list-layout .listing-item:focus .listing-item-tags::before {
    top: 100%;
    transition: .1s all ease-in-out
  }

  body.listeo-child-theme .listings-container.list-layout .listing-item:hover .listing-item-tags,
  body.listeo-child-theme .listings-container.list-layout .listing-item:focus .listing-item-tags {
    transition: max-height 1s ease-in-out;
    max-height: 1000px;
  }

  .listing-item-container.list-layout .listing-item-content {
    display: flex;
    justify-content: center;
    flex-direction: column;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  body.listeo-child-theme .fs-container section.search {
    width: 100%;
    margin-right: 20px;
    margin-left: 20px;
    margin-bottom: 25px;
  }

  body.listeo-child-theme .fs-container .row.fs-listings {
    padding-right: 5px;
  }

  .listing-item-container.list-layout .listing-item-image {
    height: auto;
  }
}

@media (min-width:1024px) {
  .mobile-filter {
    display: none;
  }
}

@media (min-width: 1024px) and (max-width: 1470px) {
  body.listeo-child-theme .fs-container .row.fs-listings {
    width: 100%;
    flex: auto;
    padding-left: 15px;
    margin-top: 20px;
  }

  body.listeo-child-theme .fs-container section.search {
    width: 100%;
    margin-right: 30px;
  }
}

@media (max-width: 1023px) {
  .button.border.mobile-refine-btn {
    display: block;
  }

  #listeo-listings-container {
    position: relative;
  }

  .loader-ajax-container {
    top: 200px;
  }

  body.listeo-child-theme .fs-container h1.page-title {
    margin-top: 45px;
  }

  body.listeo-child-theme .fs-container section.search {
    margin-bottom: 30px;
    display: none;
    margin-left: 20px;
    width: 100%;
    margin-right: 20px;
  }

  .fs-container .fs-switcher {
    margin-bottom: 0;
  }

  .mobile-filter {
    width: 100%;
    display: flex;
    gap: 8px;
    margin-left: 20px;
    margin-right: 20px;
    margin-bottom: 20px;
    align-items: center;
  }

  .filter-results-found {
    font-size: 14px;
    line-height: 1.3;
    flex: 1;
    margin-bottom: 0;
  }

  .panel-wrapper .drilldown-menu .menu-panel.open {
    margin-top: 0;
    width: 100%;
    left: 0;
  }

  .drilldown-menu-panel,
  .panel-wrapper .drilldown-menu {
    position: relative;
  }
}

@media (max-width: 768px) {
  body.listeo-child-theme .fs-container .row.fs-listings {
    padding-left: 5px;
    padding-right: 5px;
  }
}