<?php
/**
 * Template Name: Events Search Page
 *
 * @package Listeo
 */

$template_loader = new Listeo_Core_Template_Loader;

get_header();

$listing_query_args = search_page_args( 'event' );

$available_query_vars = Listeo_Core_Search::build_available_query_vars();
foreach ( $available_query_vars as $key => $meta_key ) {

	if ( isset( $_GET[ $meta_key ] ) && $_GET[ $meta_key ] != -1 && ! str_starts_with( $meta_key, 'tax-' ) ) {

		$listing_query_args['meta_query'][] = array(
			'key' => $meta_key,
			'value' => query_var_value_fix( $_GET[ $meta_key ] ),
			'compare' => '='
		);
	}
}

$listing_query = new WP_Query( $listing_query_args );
?>
<!-- Content
================================================== -->
<div class="fs-container">

	<div class="fs-inner-container content">
		<div class="fs-content">

			<!-- Search / End -->

			<?php $content_layout = get_option( 'pp_listings_layout', 'list' ); ?>
			<section class="listings-container margin-top-45">
				<!-- Sorting / Layout Switcher -->
				<div class="row fs-switcher">
					<?php
					do_action( 'listeo_archive_split_before_title' );
					if ( get_option( 'listeo_show_archive_title' ) == 'enable' ) { ?>
						<div class="col-md-12">
							<?php
							$title = get_option( 'listeo_listings_archive_title' );
							if ( ! empty( $title ) && is_post_type_archive( 'listing' ) ) { ?>
								<h1 class="page-title"><?php echo esc_html( $title ); ?></h1>
							<?php } else {
								the_archive_title( '<h1 class="page-title">', '</h1>' );
							} ?>
						</div>
					<?php }
					do_action( 'listeo_archive_split_after_title' ); ?>

					<?php $top_buttons = get_option( 'listeo_listings_top_buttons' );

					if ( $top_buttons == 'enable' ) {
						$top_buttons_conf = get_option( 'listeo_listings_top_buttons_conf' );
						if ( is_array( $top_buttons_conf ) && ! empty( $top_buttons_conf ) ) {

							if ( ( $key = array_search( 'radius', $top_buttons_conf ) ) !== false ) {
								unset( $top_buttons_conf[ $key ] );
							}
							if ( ( $key = array_search( 'filters', $top_buttons_conf ) ) !== false ) {
								unset( $top_buttons_conf[ $key ] );
							}
							$list_top_buttons = implode( "|", $top_buttons_conf );
						} else {
							$list_top_buttons = '';
						}
						?>

						<?php do_action( 'listeo_before_archive', $content_layout, $list_top_buttons ); ?>

						<?php
					} ?>

				</div>

				<!-- Search -->
				<div class="mobile-filter">
					<button class="btn-show-filter button"
						onclick="document.querySelector('section.search').classList.toggle('show')"><i
							class="fa fa-filter"></i> Refine Results</button>
					<p class="filter-results-found">
						<?php
						$found = absint( $listing_query->found_posts );
						printf( '<span class="results-found">%d </span>', $found );

						$result_text = 1 === $found ? esc_html__( 'Result', 'listeo' ) : esc_html__( 'Results', 'listeo' );
						printf( ' %s', $result_text );
						?>
					</p>
				</div>
				<?php
				// Sanitize the sidebar class from GET parameter
				$sidebar_class = isset( $_GET['sidebar'] ) ? sanitize_html_class( $_GET['sidebar'] ) : '';
				?>
				<section class="search <?php echo esc_attr( $sidebar_class ); ?>">
					<h2 class="search-title">Filter Results</h2>
					<a href="#" id="show-map-button" class="show-map-button"
						data-enabled="<?php esc_attr_e( 'Show Map ', 'listeo' ); ?>"
						data-disabled="<?php esc_attr_e( 'Hide Map ', 'listeo' ); ?>"><?php esc_html_e( 'Show Map ', 'listeo' ); ?></a>
					<div class="row">
						<div class="col-md-12">

							<?php echo do_shortcode( '[listeo_search_form source="events-sidebar" more_custom_class="margin-bottom-30"]' ); ?>

							<button type="button" class="button border mobile-refine-btn">Apply Filters</button>
						</div>
					</div>

				</section>

				<!-- Listings -->
				<div class=" row fs-listings">

					<?php

					switch ( $content_layout ) {
						case 'list':
						case 'grid':
							$container_class = sprintf( '%s-layout', $content_layout );
							break;

						case 'compact':
							$container_class = $content_layout;
							break;

						default:
							$container_class = 'list-layout';
							break;
					}

					$data = '';

					$data .= sprintf( ' data-region="%s" ', esc_attr( get_query_var( 'region' ) ) );
					$data .= sprintf( ' data-category="%s" ', esc_attr( get_query_var( 'listing_category' ) ) );
					$data .= sprintf( ' data-feature="%s" ', esc_attr( get_query_var( 'listing_feature' ) ) );
					$data .= sprintf( ' data-_listing_type="%s" ', esc_attr( 'event' ) );
					$orderby_value = isset( $_GET['listeo_core_order'] ) ? sanitize_text_field( $_GET['listeo_core_order'] ) : get_option( 'listeo_sort_by', 'date' );
					?>
					<!-- Listings -->
					<div data-grid_columns="2" <?php echo wp_kses_post( $data ); ?>
						data-orderby="<?php echo esc_attr( $orderby_value ); ?>"
						data-style="<?php echo esc_attr( $content_layout ); ?>"
						class="listings-container <?php echo esc_attr( $container_class ); ?>"
						id="listeo-listings-container">
						<div class="loader-ajax-container" style="">
							<div class="loader-ajax"></div>
						</div>
						<?php
						if ( $listing_query->have_posts() ) :

							/* Start the Loop */
							while ( $listing_query->have_posts() ) :
								$listing_query->the_post();

								switch ( $content_layout ) {
									case 'list':
										$template_loader->get_template_part( 'content-listing' );
										break;

									case 'grid':
										echo '<div class="col-lg-6 col-md-12"> ';
										$template_loader->get_template_part( 'content-listing-grid' );
										echo '</div>';
										break;

									case 'compact':
										echo '<div class="col-lg-6 col-md-12"> ';
										$template_loader->get_template_part( 'content-listing-compact' );
										echo '</div>';
										break;

									default:
										$template_loader->get_template_part( 'content-listing' );
										break;
								}

							endwhile;

						else :

							$template_loader->get_template_part( 'archive/no-found' );

						endif; ?>

						<div class="clearfix"></div>
					</div>
					<?php $ajax_browsing = get_option( 'listeo_ajax_browsing' ); ?>
					<div class="pagination-container margin-top-45 margin-bottom-60 row  <?php if ( isset( $ajax_browsing ) && $ajax_browsing == 'on' ) {
						echo esc_attr( 'ajax-search' );
					} ?>">
						<nav class="pagination col-md-12">
							<?php
							if ( $ajax_browsing == 'on' ) {
								$pages = $listing_query->max_num_pages;
								echo listeo_core_ajax_pagination( $pages, 1 );
							} else
								if ( function_exists( 'wp_pagenavi' ) ) {
									wp_pagenavi( [ 
										'next_text' => '<i class="fa fa-chevron-right"></i>',
										'prev_text' => '<i class="fa fa-chevron-left"></i>',
										'use_pagenavi_css' => false,
									] );
								} else {
									the_posts_navigation();
								} ?>
						</nav>
					</div>
					<?php if ( term_description() ) { ?>
						<div class="row term-description" style="padding: 15px 55px 25px;">
							<?php echo term_description(); ?>
						</div>
					<?php } ?>

					<div class="hidden-search-map-results">
						<?php
						// Clone the main query
						$archive_all_posts_query_args = $listing_query->query_vars;

						// Remove the paged parameter to avoid conflicts with posts_per_page = -1
						unset( $archive_all_posts_query_args['paged'] );

						// Set posts_per_page to 1000000
						$archive_all_posts_query_args['posts_per_page'] = 1000000;

						$archive_all_posts_query = new WP_Query( $archive_all_posts_query_args );

						if ( $archive_all_posts_query->have_posts() ) :

							/* Start the Loop */
							while ( $archive_all_posts_query->have_posts() ) :
								$archive_all_posts_query->the_post();

								$template_loader->get_template_part( 'content-listing-archive-custom' );

							endwhile;
						endif;

						wp_reset_postdata(); ?>
					</div>
				</div>
			</section>

		</div>
	</div>
	<div class="fs-inner-container map-fixed">

		<!-- Map -->
		<div id="map-container" class="">
			<div id="map" class="split-map" data-map-zoom="<?php echo get_option( 'listeo_map_zoom_global', 9 ); ?>"
				data-map-scroll="true">
				<!-- map goes here -->
			</div>

		</div>

	</div>
</div>

<div class="clearfix"></div>

<?php get_footer(); ?>