<?php

if ( ! class_exists( 'WP_List_Table' ) ) {
	require_once ABSPATH . 'wp-admin/includes/class-wp-list-table.php';
}

add_action( 'admin_menu', 'custom_create_edited_listings_admin_page' );
function custom_create_edited_listings_admin_page() {
	add_submenu_page(
		'edit.php?post_type=listing',
		'Edited Listings',
		'Edited Listings' . ' <span class="awaiting-mod">' . custom_get_edited_count() . '</span>',
		'manage_options',
		'edited-listings',
		'custom_edited_listings_page'
	);
}

function custom_get_edited_count() {
	$posts = get_posts( [ 
		'post_type' => 'listing',
		'meta_key' => '_listing_edited',
		'meta_value' => 1,
		'posts_per_page' => -1,
		'fields' => 'ids',
	] );
	return count( $posts );
}

// Handle bulk action
// add_action( 'admin_init', 'custom_handle_edited_listings_bulk_action' );
// function custom_handle_edited_listings_bulk_action() {
// 	// Check nonce on either POST or GET
// 	if ( isset( $_REQUEST['_wpnonce'] ) && ! current_user_can( 'manage_options' ) ) {
// 		return;
// 	}
// 	// Verify the same nonce you used in wp_nonce_url()
// 	if ( isset( $_REQUEST['_wpnonce'] ) && ! wp_verify_nonce( sanitize_text_field( $_REQUEST['_wpnonce'] ), 'bulk-listing' ) ) {
// 		return;
// 	}

// 	// Identify action
// 	$action = '';
// 	if ( isset( $_POST['action'] ) && 'mark_reviewed' === $_POST['action'] ) {
// 		$action = 'mark_reviewed';
// 		$ids = isset( $_POST['listing'] ) ? (array) $_POST['listing'] : array();
// 	} elseif ( isset( $_GET['action'] ) && 'mark_reviewed' === $_GET['action'] && isset( $_GET['listing'] ) ) {
// 		$action = 'mark_reviewed';
// 		$ids = array( absint( $_GET['listing'] ) );
// 	}

// 	if ( 'mark_reviewed' !== $action || empty( $ids ) ) {
// 		return;
// 	}

// 	// Process each ID
// 	foreach ( $ids as $id ) {
// 		delete_post_meta( $id, '_listing_edited' );
// 		delete_post_meta( $id, '_listing_edited_date' );
// 	}

// 	// Redirect back to remove query args
// 	$redirect = remove_query_arg( array( 'action', 'listing', '_wpnonce' ) );
// 	wp_safe_redirect( $redirect );
// 	exit;
// }

// Page callback: instantiate and render the table
function custom_edited_listings_page() {
	echo '<div class="wrap"><h1>Edited Listings</h1>';

	$table = new Custom_Edited_Listings_Table();
	$table->prepare_items();
	echo '<form method="post">';
	$table->search_box( 'Search Listings', 'listing' );
	$table->display();
	echo '</form></div>';
}


// The List Table class
class Custom_Edited_Listings_Table extends WP_List_Table {

	public function __construct() {
		parent::__construct( [ 
			'singular' => 'listing',
			'plural' => 'listings',
			'ajax' => false,
		] );
	}

	public function get_columns() {
		$cols = [ 
			'cb' => '<input type="checkbox" />',
			'title' => 'Listing',
			'author' => 'Author',
			'edited_date' => 'Edited Date',
		];
		return $cols;
	}

	protected function column_cb( $item ) {
		return sprintf(
			'<input type="checkbox" name="listing[]" value="%s" />',
			$item->ID
		);
	}

	public function get_sortable_columns() {
		return [ 
			'title' => [ 'title', true ],
			'author' => [ 'post_author', false ],
			'edited_date' => [ 'edited_date', false ],
		];
	}

	public function column_title( $item ) {
		$edit_link = get_edit_post_link( $item->ID );
		$actions = [ 
			'view' => sprintf( '<a href="%s">View</a>', get_permalink( $item->ID ) ),
			'edit' => sprintf( '<a href="%s">Edit</a>', $edit_link ),
			'review' => sprintf(
				'<a href="%s">%s</a>',
				wp_nonce_url(
					add_query_arg(
						[ 'action' => 'mark_reviewed', 'listing' => $item->ID ],
						admin_url( 'edit.php?post_type=listing&page=edited-listings' )
					),
					'bulk-listing'
				),
				'Mark Reviewed'
			),
		];
		return sprintf( '<strong><a href="%s">%s</a></strong> %s',
			$edit_link,
			esc_html( get_the_title( $item->ID ) ),
			$this->row_actions( $actions )
		);
	}

	public function column_author( $item ) {
		$user = get_userdata( $item->post_author );
		return $user ? esc_html( $user->display_name ) : '-';
	}

	public function column_edited_date( $item ) {
		return esc_html( get_post_meta( $item->ID, '_listing_edited_date', true ) );
	}

	public function get_bulk_actions() {
		return [ 
			'mark_reviewed' => 'Mark Reviewed',
		];
	}

	public function process_bulk_action() {
		// 1) Figure out which action it is
		$action = $this->current_action(); // catches both GET & POST

		if ( 'mark_reviewed' !== $action ) {
			return;
		}

		// 2) Validate the proper nonce
		// - bulk checkbox submit: nonce action = 'bulk-' . $plural (i.e. 'bulk-listings')
		// - single row link:      nonce action = 'bulk-listing'
		$is_bulk = isset( $_POST['_wpnonce'] );
		$nonce = isset( $_REQUEST['_wpnonce'] ) ? $_REQUEST['_wpnonce'] : '';

		if ( $is_bulk ) {
			// Bulk submission
			if ( ! wp_verify_nonce( $nonce, 'bulk-' . $this->_args['plural'] ) ) {
				wp_die( 'Security check failed (bulk).' );
			}
		} else {
			// Row‐action link via GET
			if ( ! wp_verify_nonce( $nonce, 'bulk-listing' ) ) {
				wp_die( 'Security check failed (row).' );
			}
		}

		// 3) Gather all the IDs
		$ids = array();
		if ( ! empty( $_REQUEST['listing'] ) ) {
			$ids = (array) $_REQUEST['listing'];
		}

		if ( empty( $ids ) ) {
			return;
		}

		// 4) Process each one
		foreach ( $ids as $id ) {
			delete_post_meta( $id, '_listing_edited' );
			delete_post_meta( $id, '_listing_edited_date' );
		}

		// 5) Redirect to clean up URL params
		$redirect = remove_query_arg( array( 'action', 'listing', '_wpnonce' ) );
		wp_safe_redirect( $redirect );
		exit;
	}

	public function prepare_items() {
		$this->process_bulk_action();

		$per_page = 20;
		$current_page = $this->get_pagenum();
		$search = (string) \WP_List_Table::get_search_query();
		$orderby = isset( $_REQUEST['orderby'] ) ? sanitize_text_field( $_REQUEST['orderby'] ) : 'edited_date';
		$order = isset( $_REQUEST['order'] ) ? sanitize_text_field( $_REQUEST['order'] ) : 'DESC';

		// Build WP_Query args
		$qargs = [ 
			'post_type' => 'listing',
			'posts_per_page' => $per_page,
			'paged' => $current_page,
			'orderby' => $orderby,
			'order' => $order,
			'meta_key' => '_listing_edited_date',
			'meta_query' => [ 
				[ 'key' => '_listing_edited', 'value' => 1, 'compare' => '=' ]
			],
		];
		if ( $search ) {
			$qargs['s'] = $search;
		}

		$query = new WP_Query( $qargs );

		$this->items = $query->posts;

		$this->_column_headers = array(
			$this->get_columns(),		// columns
			array(),			// hidden
			$this->get_sortable_columns(),	// sortable
		);

		$this->set_pagination_args( [ 
			'total_items' => (int) $query->found_posts,
			'per_page' => $per_page,
			'total_pages' => (int) $query->max_num_pages,
		] );
	}
}