<?php

// Redirect to homepage if wrong package is selected
add_action( 'wp', 'redirect_for_wrong_package_selection' );
function redirect_for_wrong_package_selection() {
	// Redirect to homepage if no package is selected
	if ( isset( $_POST['package'] ) && ! $_POST['package'] ) {
		wp_safe_redirect( home_url() );
		exit;
	}

	// Redirect to homepage if other listing package is selected for service type
	if ( isset( $_POST['package'] ) && $_POST['package'] === custom_get_package( 'other-listings' ) && ! is_other_listing_type( $_POST['_listing_type'] ) ) {
		wp_safe_redirect( home_url() );
		exit;
	}

	// Redirect to homepage if other service packages is not selected for other listing types
	if ( isset( $_POST['package'] ) && $_POST['package'] !== custom_get_package( 'other-listings' ) && is_other_listing_type( $_POST['_listing_type'] ) ) {
		wp_safe_redirect( home_url() );
		exit;
	}

	$update = isset( $_GET['update'] ) ? intval( $_GET['update'] ) : 0;
	$listing_id = isset( $_GET['listing_id'] ) ? intval( $_GET['listing_id'] ) : 0;
	if ( $update === 1 ) {
		if ( ! $listing_id || ! is_no_package_listing( $listing_id ) ) {
			wp_safe_redirect( home_url() );
			exit;
		}
	}
}

add_action( 'template_redirect', 'skip_checkout_for_other_listings_package' );
function skip_checkout_for_other_listings_package() {
	// Only target the checkout (but not the order-received “Thank You” endpoint)
	if ( ! function_exists( 'is_checkout' )
		|| ! is_checkout()
		|| is_wc_endpoint_url( 'order-received' )
		// Bail if cart doesn’t have our target product
		|| ! cart_has_product( custom_get_package( 'other-listings', true ) )
	) {
		return;
	}

	// Proceed to auto-create the order
	auto_create_and_complete_order_for_product( custom_get_package( 'other-listings', true ) );
}

function cart_has_product( $product_id ) {
	foreach ( WC()->cart->get_cart() as $cart_item ) {
		if ( intval( $cart_item['product_id'] ) === intval( $product_id ) ) {
			return true;
		}
	}
	return false;
}

function auto_create_and_complete_order_for_product( $product_id ) {
	// 1. Create a new order
	$order = wc_create_order();

	// 2. Add the product to it (quantity = 1)
	$product = wc_get_product( $product_id );
	if ( ! $product ) {
		return; // safety check
	}
	$order->add_product( $product, 1 );

	// 3. Attach to current user
	if ( is_user_logged_in() ) {
		$order->set_customer_id( get_current_user_id() );
	}

	// 4. Calculate totals & mark paid
	$order->calculate_totals();
	$order->payment_complete(); // WC marks it “processing”/“completed”

	// 5. Empty the cart
	WC()->cart->empty_cart();

	// 6. Redirect to the order-received page
	wp_safe_redirect( $order->get_checkout_order_received_url() );
	exit;
}


// Check package id field listeo form submit
add_filter( 'submit_listing_form_validate_fields', 'custom_validate_package_id', 10, 3 );
function custom_validate_package_id( $bool, $fields, $values ) {
	if ( isset( $_POST['_listing_type'] ) && $_POST['_listing_type'] === 'service' ) {
		if ( isset( $_POST['listing_id'] ) && $_POST['listing_id'] == 0 ) {

			$package_id = $_POST['package'] ?? "";

			if ( empty( $package_id ) ) {
				return new WP_Error( 'error', __( 'Invalid Package', 'listeo_core' ) );
			} else if ( str_starts_with( $package_id, 'user-' ) ) { // for already purchased packages
				$package_id = substr( $package_id, 5 );
				$user_packages = listeo_core_user_packages( get_current_user_id() );
				$package_id = $user_packages[ $package_id ]->product_id ?? 0;

				if ( ! $package_id ) {
					return new WP_Error( 'error', __( 'Invalid Package', 'listeo_core' ) );
				}
			} else {
				$package = wc_get_product( $package_id );

				if ( ! $package || ( ! $package->is_type( 'listing_package' ) && ! $package->is_type( 'listing_package_subscription' ) ) ) {
					return new WP_Error( 'error', __( 'Invalid Package', 'listeo_core' ) );
				}
			}
		}
	}


	return true;
}