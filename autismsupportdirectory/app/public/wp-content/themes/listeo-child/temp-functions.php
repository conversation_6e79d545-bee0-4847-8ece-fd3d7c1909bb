<?php

// Check for draft listings
$draft_listings = get_posts( array(
	'post_type' => 'listing',
	'post_status' => 'draft',
	'author' => get_current_user_id(),
	'posts_per_page' => -1,
	'meta_query' => array(
		array(
			'key' => '_listing_draft_saved',
			'compare' => 'EXISTS',
		)
	)
) );

// If we have drafts and we're not editing a specific listing, show drafts
if ( ! empty( $draft_listings ) && ( ! isset( $_GET['action'] ) || $_GET['action'] != 'edit' ) ) {
	?>
	<div class="notification notice">
		<p><?php esc_html_e( 'You have saved drafts. Would you like to continue editing one of them?', 'listeo_core' ); ?>
		</p>
		<ul class="saved-drafts-list">
			<?php foreach ( $draft_listings as $draft ) :
				$saved_date = get_post_meta( $draft->ID, '_listing_draft_saved', true );
				$saved_date = ! empty( $saved_date ) ? date_i18n( get_option( 'date_format' ) . ' ' . get_option( 'time_format' ), strtotime( $saved_date ) ) : '';
				$title = ! empty( $draft->post_title ) ? $draft->post_title : __( 'Untitled Draft', 'listeo_core' );
				?>
				<li>
					<a
						href="<?php echo esc_url( add_query_arg( array( 'action' => 'edit', 'listing_id' => $draft->ID ), get_permalink() ) ); ?>">
						<?php echo esc_html( $title ); ?>
						<?php if ( $saved_date ) : ?>
							<span
								class="saved-date"><?php echo esc_html( sprintf( __( 'Last saved on %s', 'listeo_core' ), $saved_date ) ); ?></span>
						<?php endif; ?>
					</a>
				</li>
			<?php endforeach; ?>
		</ul>
	</div>
	<?php
}

// If editing a draft, load the saved step
if ( isset( $data->listing_id ) && get_post_status( $data->listing_id ) == 'draft' ) {
	$saved_step = get_post_meta( $data->listing_id, '_listing_draft_step', true );
	if ( ! empty( $saved_step ) ) {
		$editing_form_script = "document.addEventListener('DOMContentLoaded', function () {
				// Set the current step to the saved step
				currentStep = " . intval( $saved_step ) . "

				// Hide all steps and show the current one
				const steps = document.querySelectorAll('.form-step');
				steps.forEach((step, index) => {
					if (index === currentStep) {
						step.classList.add('active');
					} else {
						step.classList.remove('active');
					}
				});

				// Update buttons and progress
				checkButtons();
				updateProgress(currentStep);
			});";

		wp_add_inline_script( 'custom-submit-validation', $editing_form_script );
	}
}