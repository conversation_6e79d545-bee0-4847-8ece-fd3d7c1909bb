<?php
/**
 * listing Submission Form
 */
if ( ! defined( 'ABSPATH' ) )
	exit;

if ( isset( $_GET["action"] ) && $_GET["action"] == 'edit' && ! listeo_core_if_can_edit_listing( $data->listing_id ) ) { ?>
	<div class="notification closeable notice">
		<?php esc_html_e( 'You can\'t edit that listing', 'listeo_core' ); ?>
	</div>
	<?php
	return;
}
$current_user = wp_get_current_user();
$roles = $current_user->roles;
$role = array_shift( $roles );
if ( ! in_array( $role, array( 'administrator', 'admin', 'owner', 'seller' ) ) ) :
	$template_loader = new Listeo_Core_Template_Loader;
	$template_loader->get_template_part( 'account/owner_only' );
	return;
endif;

// TODO: Add draft code

/* Get the form fields */
$fields = array();
if ( isset( $data ) ) :
	$fields = ( isset( $data->fields ) ) ? $data->fields : '';
endif;

$package = $_POST['package'] ?? "";
$listing_id = $_GET['listing_id'] ?? 0;
if ( $listing_id != 0 ) { // if editing a listing
	$user_package = get_post_meta( $listing_id, '_user_package_id', true );

	if ( $user_package ) {
		$package = listeo_core_get_package_by_id( $user_package );
		$package = $package->product_id ?? 0;
	}
}

if ( str_starts_with( $package, 'user-' ) ) { // for already purchased packages
	$package = substr( $package, 5 );
	$user_packages = listeo_core_user_packages( get_current_user_id() );
	$package = $user_packages[ $package ]->product_id ?? 0;
}

$package_name = custom_get_package_name( $package );

/* Remove fields based on package */
// PLATINUM package
if ( $package === custom_get_package( 'platinum' ) ) {
	if ( isset( $fields['basic_info'] ) && isset( $fields['basic_info']['fields'] ) ) {
		if ( isset( $fields['basic_info']['fields']['service_category'] ) ) {
			$fields['basic_info']['fields']['service_category']['tooltip'] = 'Up to 10 Service Categories can be selected for a Featured Listing';
		}

		if ( isset( $fields['basic_info']['fields']['tax-region'] ) ) {
			$fields['basic_info']['fields']['tax-region']['tooltip'] = 'Up to 10 Service Regions can be selected for a Featured Listing';
		}
	}

	if ( isset( $fields['other_details'] ) && isset( $fields['other_details']['fields'] ) ) {
		if ( isset( $fields['other_details']['fields']['tax-service_category'] ) ) {
			$fields['other_details']['fields']['tax-service_category']['tooltip'] = 'Up to 5 Online Service Categories can be selected for a Featured Listing';
		}
	}
}

// PREMIUM package
// TODO Remove after new package is created
if ( $package === custom_get_package( 'premium' ) || $package === '984' ) { // 984 added for backwards compatibility
	if ( isset( $fields['basic_info'] ) && isset( $fields['basic_info']['fields'] ) ) {
		if ( isset( $fields['basic_info']['fields']['service_category'] ) ) {
			$fields['basic_info']['fields']['service_category']['tooltip'] = 'Up to 10 Service Categories can be selected for a Premium Listing';
		}

		if ( isset( $fields['basic_info']['fields']['tax-region'] ) ) {
			$fields['basic_info']['fields']['tax-region']['tooltip'] = 'Up to 10 Service Regions can be selected for a Premium Listing';
		}
	}

	if ( isset( $fields['other_details'] ) && isset( $fields['other_details']['fields'] ) ) {
		if ( isset( $fields['other_details']['fields']['tax-service_category'] ) ) {
			$fields['other_details']['fields']['tax-service_category']['tooltip'] = 'Up to 5 Online Service Categories can be selected for a Premium Listing';
		}
	}
}

// BASIC package
if ( $package === custom_get_package( 'basic' ) ) {
	if ( isset( $fields['basic_info'] ) && isset( $fields['basic_info']['fields'] ) ) {
		if ( isset( $fields['basic_info']['fields']['service_category'] ) ) {
			$fields['basic_info']['fields']['service_category']['tooltip'] = 'Only two Service Categories can be selected in a Basic Listing';
		}

		if ( isset( $fields['basic_info']['fields']['tax-region'] ) ) {
			$fields['basic_info']['fields']['tax-region']['tooltip'] = 'Only one Region Category can be selected in a Basic Listing';
		}
	}

	if ( isset( $fields['other_details'] ) && isset( $fields['other_details']['fields'] ) ) {
		if ( isset( $fields['other_details']['fields']['tax-service_category'] ) ) {
			$fields['other_details']['fields']['tax-service_category']['type'] = "read-only";
			$fields['other_details']['fields']['tax-service_category']['label'] = "Basic Listings do not include Online Service Categories.";
			$fields['other_details']['fields']['tax-service_category']['value'] = "";
		}
	}

	if ( isset( $fields['details'] ) && isset( $fields['details']['fields'] ) ) {
		if ( isset( $fields['details']['fields']['_email_contact_widget'] ) ) {
			unset( $fields['details']['fields']['_email_contact_widget'] );
		}
	}
}

/* Determine the type of form */
if ( isset( $_GET["action"] ) ) {
	$form_type = $_GET["action"];
} else {
	$form_type = 'submit';
}
?>

<?php
if ( isset( $_POST['_listing_type'] ) ) {
	$listing_type = $_POST['_listing_type'];
} else {
	$listing_type = get_post_meta( $data->listing_id, '_listing_type', true );
	if ( empty( $listing_type ) ) {
		$listing_types = get_option( 'listeo_listing_types', array( 'service', 'rental', 'event' ) );
		if ( is_array( $listing_types ) && sizeof( $listing_types ) == 1 ) {
			$listing_type = $listing_types[0];
		} else {
			$listing_type = 'service';
		}

	}
}
?>

<div
	class="submit-page <?php echo esc_attr( 'type-' . $listing_type ); ?> package-<?php echo esc_attr( $package_name ); ?>">
	<?php if ( $form_type === 'edit' ) {
		?>
		<div class="notification closeable notice">
			<p><?php esc_html_e( 'You are currently editing:', 'listeo_core' );
			if ( isset( $data->listing_id ) && $data->listing_id != 0 ) {
				$listing = get_post( $data->listing_id );
				echo ' <a href="' . get_permalink( $data->listing_id ) . '">' . $listing->post_title . '</a>';
			} ?></p>
		</div>
	<?php } ?>
	<?php
	if ( isset( $data->listing_edit ) && $data->listing_edit ) {
		?>
		<div class="notification closeable notice">
			<?php printf( '<p><strong>' . __( "You are editing an existing listing. %s", 'listeo_core' ) . '</strong></p>', '<a href="?new=1&key=' . $data->listing_edit . '">' . __( 'Add A New Listing', 'listeo_core' ) . '</a>' ); ?>
		</div>
	<?php }
	?>

	<div class="progress-container" style="display: none;">
		<div class="progress-bar">
			<?php
			$sections_text = listing_types_sections_text( $listing_type );

			$sections_count = count( $sections_text );

			for ( $i = 0; $i < $sections_count; $i++ ) : ?>
				<div class="progress-step"><?php echo $i + 1; ?></div>
				<?php if ( $i != $sections_count - 1 ) : ?>
					<div class="progress-line"></div>
				<?php endif; ?>
			<?php endfor; ?>
		</div>
		<div class="progress-text">
			<?php
			foreach ( $sections_text as $section_text ) : ?>
				<div class="progress-step-text"><span><?php echo $section_text; ?></span></div>
			<?php endforeach; ?>
		</div>
	</div>

	<form action="<?php echo esc_url( $data->action ); ?>" method="post" id="submit-listing-form"
		class="listing-manager-form" enctype="multipart/form-data">

		<?php
		foreach ( $fields as $key => $section ) :

			?>
			<!-- Section -->
			<?php
			if ( isset( $data->listing_id ) ) {
				$switcher_value = get_post_meta( $data->listing_id, '_' . $key . '_status', true );
				if ( is_array( $switcher_value ) ) {
					$switcher_value = array_shift( $switcher_value );
				}
			} else {
				$switcher_value = false;
			}

			?>

			<div class="add-listing-section form-step row <?php echo esc_attr( ' ' . $key . ' ' );
			if ( get_option( 'listeo_dynamic_features' ) == 'on' ) {
				echo "dynamic-features";
			}
			if ( isset( $section['onoff'] ) && $section['onoff'] == true && ! empty( $switcher_value ) ) {
				echo esc_attr( 'switcher-on' );
			} ?>">

				<!-- Headline -->
				<div class="add-listing-headline <?php if ( isset( $section['class'] ) )
					echo esc_html( $section['class'] ); ?>">
					<h3>
						<?php if ( isset( $section['icon'] ) && ! empty( $section['icon'] ) ) : ?><i
								class="<?php echo esc_html( $section['icon'] ); ?>"></i> <?php endif; ?>
						<?php if ( isset( $section['title'] ) )
							echo esc_html( $section['title'] ); ?>
						<?php if ( $key == "slots" ) : ?>
							<br>
							<span id="add-listing-slots-notice">
								<?php esc_html_e( "By default booking widget in your listing has time picker. Enable this section to configure time slots.", 'listeo_core' ); ?>
							</span>
						<?php endif; ?>
						<?php if ( $key == "availability_calendar" ) : ?>
							<br><span
								id="add-listing-slots-notice"><?php esc_html_e( "Click date in calendar to mark the day as unavailable.", 'listeo_core' ); ?>
							</span>
						<?php endif; ?>
					</h3>
					<?php if ( isset( $section['onoff'] ) && $section['onoff'] == true ) : ?>
						<!-- Switcher -->
						<?php
						if ( isset( $data->listing_id ) ) {
							$value = get_post_meta( $data->listing_id, '_' . $key . '_status', true );
							//if value is array, take the first key value
							if ( is_array( $value ) ) {
								$value = array_shift( $value );
							}
							if ( $value === false && isset( $section['onoff_state'] ) && $section['onoff_state'] == 'on' ) {
								$value = 'on';
							}

						} else {
							$value = false;

							if ( isset( $section['onoff_state'] ) && $section['onoff_state'] == 'on' ) {
								$value = 'on';
							}
						}

						?>
						<label class="switch"><input <?php checked( $value, 'on' ) ?>
								id="_<?php echo esc_attr( $key ) . '_status'; ?>"
								name="_<?php echo esc_attr( $key ) . '_status'; ?>" type="checkbox"><span
								class="slider round"></span></label>
					<?php endif; ?>

				</div>
				<?php if ( $key == "booking" ) : ?>
					<div class="notification notice margin-top-40 margin-bottom-20">

						<p><?php esc_html_e( "By turning on switch on the right, you'll enable booking feature, it will add Booking widget on your listing. You'll see more configuration settings below.", 'listeo_core' ); ?>
						</p>

					</div>
				<?php endif; ?>
				<?php if ( $key == "location" ) : ?>


					<div class="col-md-12">
						<div id="submit_map"></div>
					</div>

				<?php endif; ?>
				<?php if ( isset( $section['onoff'] ) && $section['onoff'] == true ) : ?>
					<div class="switcher-content">
					<?php endif; ?>
					<?php foreach ( $section['fields'] as $key => $field ) : ?>

						<?php if ( isset( $field['type'] ) && $field['type'] == "skipped" ) {
							continue;
						}
						$field['submit_type'] = $listing_type;

						// if( isset($field['before_row']) ) : 
						// 	echo $field['before_row'].' <!-- before row '.$field['label'].' -->';
						// endif; 
						?>
						<?php
						if ( isset( $field['render_row_col'] ) && ! empty( $field['render_row_col'] ) ) :
							listeo_core_render_column( $field['render_row_col'], $field['name'] );
						else :
							listeo_core_render_column( 12, $field['name'] );
						endif;
						?>
						<?php if ( isset( $field['type'] ) && $field['type'] != 'hidden' ) : ?>

							<label class="label-<?php echo esc_attr( $key ); ?>" for="<?php echo esc_attr( $key ); ?>">
								<?php echo stripslashes( $field['label'] ) . apply_filters( 'submit_listing_form_required_label', isset( $field['required'] ) ? '' : ' <small>' . esc_html__( '(optional)', 'workscout' ) . '</small>', $field ); ?>
								<?php if ( isset( $field['tooltip'] ) && ! empty( $field['tooltip'] ) ) { ?>
									<i class="tip" data-tip-content="<?php ( esc_attr_e( stripslashes( $field['tooltip'] ) ) ); ?>"></i>
								<?php } ?>
							</label>
						<?php endif; ?>

						<?php
						$template_loader = new Listeo_Core_Template_Loader;
						$template_loader->set_template_data( array( 'key' => $key, 'field' => $field, ) )->get_template_part( 'form-fields/' . $field['type'] );
						?>
					</div>
					<?php
					// if( isset($field['render_row_col']) && !empty($field['render_row_col']) ) : 
					// 	echo "</div>  <!-- close row ".$field['name']." -->";
					// else:
					// 	echo "</div>  <!-- close row ".$field['name']." -->";
			
					// endif; 
					?>


				<?php endforeach; ?>
				<?php if ( isset( $section['onoff'] ) && $section['onoff'] == true ) : ?>
				</div>
			<?php endif; ?>
	</div> <!-- end section  -->


<?php endforeach; ?>

<div class="divider margin-top-40"></div>

<input type="hidden" name="package" value="<?php echo esc_attr( $_POST['package'] ?? '' ); ?>">
<input type="hidden" name="_listing_type" value="<?php echo esc_attr( $listing_type ); ?>">
<input type="hidden" name="listeo_core_form" value="<?php echo $data->form; ?>" />
<input type="hidden" name="listing_id" value="<?php echo esc_attr( $data->listing_id ); ?>" />
<input type="hidden" name="step" value="<?php echo esc_attr( $data->step ); ?>" />
<input type="hidden" name="submit_listing" value="<?php echo esc_attr( $data->submit_button_text ); ?>" />

<div class="step-buttons">
	<button type="button" class="button back-btn" style="display: none;">
		<i class="fa fa-arrow-circle-left"></i> Back
	</button>
	<button type="button" class="button next-btn" style="display: block;">
		Next <i class="fa fa-arrow-circle-right"></i>
	</button>
	<button type="submit" value="<?php echo esc_attr( $data->submit_button_text ); ?>" name="submit_listing"
		class="button submit-btn" style="display: none;">
		<?php echo esc_attr( $data->submit_button_text ); ?> <i class="fa fa-arrow-circle-right"></i>
	</button>
</div>

</form>
<?php

// PLATINUM package
if ( $package === custom_get_package( 'platinum' ) ) { ?>
	<script type="text/javascript">
		let selectedCategoriesCount = 0;
		let selectedOnlineServicesCount = 0;

		document.querySelectorAll('.listeo_core-term-checklist-service_category input').forEach(function (input) {
			input.addEventListener('change', function () {
				if (!input.nextElementSibling.textContent.toLowerCase().includes('assessments')) {
					if (input.checked === false) {
						selectedCategoriesCount--;
					} else if (selectedCategoriesCount >= 10) {
						input.checked = false;
						alert('You can only select up to 10 service categories.');
					} else {
						selectedCategoriesCount++;
					}
				}
			});
		});

		document.querySelectorAll('.listeo_core-term-checklist-tax-service_category input').forEach(function (input) {
			input.addEventListener('change', function () {
				if (!input.nextElementSibling.textContent.toLowerCase().includes('assessments')) {
					if (input.checked === false) {
						selectedOnlineServicesCount--;
					} else if (selectedOnlineServicesCount >= 10) {
						input.checked = false;
						alert('You can only select up to 10 online services.');
					} else {
						selectedOnlineServicesCount++;
					}
				}
			});
		});

		document.querySelectorAll('.listeo_core-term-checklist-tax-region input').forEach(function (input) {
			input.addEventListener('change', function () {
				if (document.querySelectorAll(".listeo_core-term-checklist-tax-region input:checked").length > 5) {
					input.checked = false;
					alert('You can only select up to 5 regions.');
				}
			});
		});
	</script>
<?php }

// PREMIUM package
// TODO Remove after new package is created 984 added for backwards compatibility
if ( $package === custom_get_package( 'premium' ) || $package === '984' ) { ?>
	<script type="text/javascript">
		let selectedCategoriesCount = 0;
		let selectedOnlineServicesCount = 0;

		document.querySelectorAll('.listeo_core-term-checklist-service_category input').forEach(function (input) {
			input.addEventListener('change', function () {
				if (!input.nextElementSibling.textContent.toLowerCase().includes('assessments')) {
					if (input.checked === false) {
						selectedCategoriesCount--;
					} else if (selectedCategoriesCount >= 10) {
						input.checked = false;
						alert('You can only select up to 10 service categories.');
					} else {
						selectedCategoriesCount++;
					}
				}
			});
		});

		document.querySelectorAll('.listeo_core-term-checklist-tax-service_category input').forEach(function (input) {
			input.addEventListener('change', function () {
				if (!input.nextElementSibling.textContent.toLowerCase().includes('assessments')) {
					if (input.checked === false) {
						selectedOnlineServicesCount--;
					} else if (selectedOnlineServicesCount >= 10) {
						input.checked = false;
						alert('You can only select up to 10 online services.');
					} else {
						selectedOnlineServicesCount++;
					}
				}
			});
		});

		document.querySelectorAll('.listeo_core-term-checklist-tax-region input').forEach(function (input) {
			input.addEventListener('change', function () {
				if (document.querySelectorAll(".listeo_core-term-checklist-tax-region input:checked").length > 5) {
					input.checked = false;
					alert('You can only select up to 5 regions.');
				}
			});
		});
	</script>
<?php }

// BASIC package
if ( $package === custom_get_package( 'basic' ) ) { ?>
	<script type="text/javascript">
		// function setWordLimit(editorId, wordLimit) {
		// 	const editor = tinymce.get(editorId);
		// 	if (editor) {
		// 		editor.on('input focusout', function () {
		// 			const content = editor.getContent({ format: 'text' }); // Get text-only content
		// 			const words = content.split(/\s+/).filter((word) => word.length > 0); // Split content into words

		// 			if (words.length > wordLimit) {
		// 				const limitedContent = words.slice(0, wordLimit).join(' '); // Join limited words back into a string
		// 				editor.setContent(limitedContent); // Set the trimmed content

		// 				alert(`Word limit of ${wordLimit} has been reached.`);
		// 			}
		// 		});
		// 	} else {
		// 		console.error('Editor not found!');
		// 	}
		// }

		// jQuery(document).ready(function () {
		// 	setWordLimit('listing_description', 100);
		// });

		document.querySelectorAll('.listeo_core-term-checklist-service_category input').forEach(function (input) {
			input.addEventListener('change', function () {
				if (document.querySelectorAll(".listeo_core-term-checklist-service_category input:checked").length > 2) {
					input.checked = false;
					alert('You can only select up to 2 service categories.');
				}
			});
		});

		document.querySelectorAll('.listeo_core-term-checklist-tax-region input').forEach(function (input) {
			input.addEventListener('change', function () {
				if (document.querySelectorAll(".listeo_core-term-checklist-tax-region input:checked").length > 1) {
					input.checked = false;
					alert('You can only select up to 1 region.');
				}
			});
		});
	</script>
<?php }
?>
</div>