<!-- Features -->
<?php

$taxonomies = get_option( 'listeo_single_taxonomies_checkbox_list', array( 'listing_feature' ) );

if ( empty( $taxonomies ) ) {
	return;
}
foreach ( $taxonomies as $tax ) {
	$term_list = get_the_terms( $post->ID, $tax );
	$tax_obj = get_taxonomy( $tax );
	$taxonomy = get_taxonomy_labels( $tax_obj );


	if ( ! empty( $term_list ) ) { ?>
		<h3 class="listing-desc-headline"><?php echo $taxonomy->name; ?></h3>

		<ul>
			<?php
			foreach ( $term_list as $term ) {
				echo '';
				$term_link = get_term_link( $term );
				if ( is_wp_error( $term_link ) )
					continue;
				$t_id = $term->term_id;

				echo '<li><a href="' . esc_url( $term_link ) . '">' . $term->name . '</a></li>';
			}
			?>
		</ul>

	<?php }
}
?>