<?php
$settings = $data->settings;
$type = $settings['type'] ?? '';
$custom_field_heading = $settings['custom_field_heading'] ?? '';
$custom_field = $settings['custom_field'] ?? '';
$custom_field_heading_2 = $settings['custom_field_heading_2'] ?? '';
$custom_field_2 = $settings['custom_field_2'] ?? '';
$hide_reviews = $settings['hide_reviews'] ?? '';
$disable_autoplay = $settings['disable_autoplay'] ?? '';
$displayed_items = $settings['displayed_items'] ?? '1';

//Get array of terms
$template_loader = new Listeo_Core_Template_Loader;
$other_listings = get_post_meta( $post->ID, '_my_listings_' . $type );
$title = get_post_meta( $post->ID, '_my_listings_title_' . $type, true );
if ( ! $title ) {
	$title = esc_html__( 'My Other ' . ucfirst( $type ), 'listeo_core' );
}

if ( $other_listings ) :
	//Query posts with tax_query. Choose in 'IN' if want to query posts with any of the terms
	//Chose 'AND' if you want to query for posts with all terms
	$args = array(
		'post_type' => 'listing',
		'ignore_sticky_posts' => 1,
		'orderby' => 'rand',
		'posts_per_page' => -1,
		'post__not_in' => array( $post->ID ),
		'post__in' => $other_listings
	);

	if ( get_option( 'listeo_single_related_current_author' ) ) {
		global $post;
		$args['author'] = $post->post_author;
	}

	$second_query = new WP_Query( $args );
	//Loop through posts and display...
	if ( $second_query->have_posts() ) { ?>
		<h3 class="desc-headline no-border margin-bottom-35 margin-top-60 print-no"><?php echo $title; ?></h3>
		<div class=" margin-bottom-35 simple-slick-carousel "
			data-slick='{"autoplay": <?php echo $disable_autoplay === 'yes' ? 'false' : 'true'; ?>,"slidesToShow": <?php echo strval( $displayed_items ) ?>,"slidesToScroll": 1}'>

			<?php
			while ( $second_query->have_posts() ) :
				$second_query->the_post();

				$custom_field_value = '';
				if ( ! empty( $custom_field ) ) {
					$custom_field_value = get_post_meta( get_the_ID(), $custom_field, true );

					if ( ! empty( $custom_field_value ) ) {
						$field = custom_listeo_get_fields()[ $custom_field ];
						$field_type = $field['type'];

						$custom_field_value = custom_listeo_field_type( $field_type, $custom_field_value, $custom_field );
					}
				}

				$custom_field_value_2 = '';
				if ( ! empty( $custom_field_2 ) ) {
					$custom_field_value_2 = get_post_meta( get_the_ID(), $custom_field_2, true );

					if ( ! empty( $custom_field_value_2 ) ) {
						$field = custom_listeo_get_fields()[ $custom_field_2 ];
						$field_type = $field['type'];

						$custom_field_value_2 = custom_listeo_field_type( $field_type, $custom_field_value_2, $custom_field_2 );
					}
				}
				?>
				<div class="fw-carousel-item">
					<?php
					$template_loader->set_template_data( [ 'custom_field_heading' => $custom_field_heading, 'custom_field_value' => $custom_field_value, 'custom_field_heading_2' => $custom_field_heading_2, 'custom_field_value_2' => $custom_field_value_2, 'hide_reviews' => $hide_reviews ] )->get_template_part( 'content-listing-grid' ); ?>
				</div>
				<?php
			endwhile;
			wp_reset_postdata();
			wp_reset_query();
			?>
		</div>
	<?php }
endif;

?>