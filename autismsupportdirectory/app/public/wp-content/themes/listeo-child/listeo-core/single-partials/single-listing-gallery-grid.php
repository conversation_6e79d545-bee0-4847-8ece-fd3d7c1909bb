<?php
$count_gallery = listeo_count_gallery_items( $post->ID );
$gallery = get_post_meta( $post->ID, '_gallery', true );
$thumbnail_id = get_post_thumbnail_id( $post->ID );

if ( empty( $gallery ) ) {
	return;
}

//do while until reach the count gallery value
$popup_gallery = array();
$grid_gallery = array_keys( $gallery );

//move the array element that has the same value as thumbnail_id to the first element of the array
if ( ( $key = array_search( $thumbnail_id, $grid_gallery ) ) !== false ) {
	unset( $grid_gallery[ $key ] );
	array_unshift( $grid_gallery, $thumbnail_id );
}

foreach ( $grid_gallery as $key => $attachment_id ) {
	if ( ! wp_attachment_is_image( $attachment_id ) ) {
		unset( $grid_gallery[ $key ] );
	} else {
		$popup_gallery[] = wp_get_attachment_image_url( $attachment_id, 'listeo-gallery' );
	}
}

?>
<div class="row">
	<div class="col-md-12">
		<?php if ( $count_gallery > 1 ) { ?>
			<a href="#" id="single-listing-grid-gallery-popup" style="display: none;"
				data-gallery="<?php echo esc_attr( json_encode( $popup_gallery ) ); ?>"
				data-gallery-count="<?php echo esc_attr( $count_gallery ); ?>" class="slg-button"><i
					class="sl sl-icon-grid"></i> <?php esc_html_e( 'Show All Photos', 'listeo_core' ); ?></a>
		<?php } ?>
		<div class="custom-gallery-justified">
			<?php foreach ( $grid_gallery as $key => $attachment_id ) {
				$image = wp_get_attachment_image_src( $attachment_id, 'listeo-gallery' ); ?>
				<a href="<?php echo esc_url( $image[0] ); ?>" class="custom-gallery-justified-img-container slg-gallery-img"
					data-grid-start-index="<?php echo esc_attr( $key ); ?>"
					style="width:<?php echo esc_attr( $image[1] * 200 / $image[2] ); ?>px;flex-grow:<?php echo esc_attr( $image[1] * 200 / $image[2] ); ?>">
					<i class="custom-gallery-justified-img-filler"
						style="padding-bottom:<?php echo esc_attr( $image[2] / $image[1] * 100 ); ?>%"></i>
					<img class="custom-gallery-justified-img" src="<?php echo esc_url( $image[0] ); ?>"
						alt="<?php esc_attr_e( 'Gallery Image', 'listeo_core' ); ?>" />
				</a>
			<?php } ?>
		</div>
	</div>
</div>