<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

$field = $data->field;
$key = $data->key;
// Get selected value
if ( isset( $field['value'] ) && ! empty( $field['value'] ) ) {
	$selected = array( $field['value'] );

} elseif ( isset( $field['default'] ) && is_int( $field['default'] ) ) {
	$selected = array( $field['default'] );

} elseif ( ! empty( $field['default'] ) && ( $term = get_term_by( 'slug', $field['default'], $field['taxonomy'] ) ) ) {

	$selected = array( $term->term_id );
} else {
	$selected = array( array() );
}

$dynamic_features = ( get_option( 'listeo_dynamic_features' ) == 'on' ) ? 'dynamic' : ''; ?>
<div
	class="<?php echo esc_attr( $dynamic_features ); ?> checkboxes <?php echo $field['required'] === "1" ? "is-required" : "" ?> in-row listeo_core-term-checklist listeo_core-term-checklist-<?php echo $key ?>">
	<?php
	require_once( ABSPATH . '/wp-admin/includes/template.php' );

	if ( empty( $field['default'] ) ) {
		$field['default'] = '';
	}

	$taxonomy = $field['taxonomy'];

	if ( $taxonomy === 'service_category' ) {
		if ( $field['name'] !== 'tax-service_category_2' ) {
			$meta_query = array(
				'relation' => 'OR',
				array(
					'key' => 'move_to_second_field',
					'value' => 'on',
					'compare' => '!='
				),
				array(
					'key' => 'move_to_second_field',
					'compare' => 'NOT EXISTS'
				)
			);
		} else {
			$meta_query = array(
				array(
					'key' => 'move_to_second_field',
					'value' => 'on',
					'compare' => '='
				)
			);
		}

		$parent_terms = get_terms( $taxonomy, array(
			'hide_empty' => false,
			'parent' => 0,
			'meta_query' => $meta_query
		) );
		foreach ( $parent_terms as $category ) {
			echo '<input value="' . $category->term_id . '" type="checkbox" name="tax_input[' . $taxonomy . '][]" id="in-' . $taxonomy . '-' . $category->term_id . '"' .
				checked( in_array( $category->term_id, $selected[0] ), true, false ) . ' /> ';
			echo '<label class="service_category-parent_checkbox" for="in-' . $taxonomy . '-' . $category->term_id . '"><strong>' . esc_html( apply_filters( 'the_category', $category->name ) ) . '</strong></label>';
			$child_terms = get_terms( $taxonomy, array(
				'hide_empty' => false,
				'parent' => $category->term_id,
			) );
			foreach ( $child_terms as $category ) {
				echo '<input value="' . $category->term_id . '" type="checkbox" name="tax_input[' . $taxonomy . '][]" id="in-' . $taxonomy . '-' . $category->term_id . '"' .
					checked( in_array( $category->term_id, $selected[0] ), true, false ) . ' /> ' .
					'<label class="service_category-child_checkbox" for="in-' . $taxonomy . '-' . $category->term_id . '">' . esc_html( apply_filters( 'the_category', $category->name ) ) . '</label>';
			}
		}
	} else {
		$class = $field['class'];
		$include_terms = [];

		// If class includes value like this one-option-(number), make it the only option
		if ( preg_match( '/one-option-(\d+)/', $class, $matches ) ) {
			$include_terms[] = (int) $matches[1];
		}

		$terms_args = array(
			'taxonomy' => $taxonomy,
			'hide_empty' => false,
		);

		if ( ! empty( $include_terms ) ) {
			$terms_args['include'] = $include_terms;
		}

		$select = is_wp_error( $selected[0] ) ? [] : $selected[0];

		$terms = get_terms( $terms_args );

		foreach ( $terms as $category ) {
			echo '<input value="' . $category->term_id . '" type="checkbox" name="tax_input[' . $taxonomy . '][]" id="in-' . $taxonomy . '-' . $category->term_id . '"' .
				checked( in_array( $category->term_id, $select ), true, false ) . ' /> ' .
				'<label for="in-' . $taxonomy . '-' . $category->term_id . '">' . esc_html( apply_filters( 'the_category', $category->name ) ) . '</label>';
		}
	}
	?>
</div>