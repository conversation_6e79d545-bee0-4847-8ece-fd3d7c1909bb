<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}
$field = $data->field;
$key = $data->key;


if ( isset( $field['description'] ) && ! empty( $field['description'] ) ) {
	echo '<div class="notification closeable notice"><p class="description" id="' . $key . '-description">' . $field['description'] . '</p></div>';
}


$editor = apply_filters( 'submit_listing_form_wp_editor_args', array(
	'textarea_name' => isset( $field['name'] ) ? $field['name'] : $key,
	'media_buttons' => false,
	'textarea_rows' => 16,
	'quicktags' => false,
	'editor_class' => isset( $field['required'] ) && $field['required'] === "1" ? 'is-required' : '',
	'tinymce' => array(
		'plugins' => 'lists,paste,tabfocus,wplink,wordpress,media,textcolor',
		'height' => 600,
		'paste_as_text' => true,
		'paste_auto_cleanup_on_paste' => true,
		'paste_remove_spans' => true,
		'paste_remove_styles' => true,
		'paste_remove_styles_if_webkit' => true,
		'paste_strip_class_attributes' => true,
		'toolbar1' => 'styleselect forecolor bold italic | bullist numlist | link unlink | undo redo | media',
		'toolbar2' => '',
		'toolbar3' => '',
		'toolbar4' => ''
	),
) );

wp_editor( isset( $field['value'] ) ? wp_kses_post( $field['value'] ) : '', $key, $editor );
?>
<div class="margin-top-30"></div>