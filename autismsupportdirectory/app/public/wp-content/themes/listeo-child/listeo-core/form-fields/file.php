<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}
$field = $data->field;
$key = $data->key;
$value = ( isset( $field['value'] ) ) ? $field['value'] : '';
$allowed_mime_types = array_keys( ! empty( $field['allowed_mime_types'] ) ? $field['allowed_mime_types'] : get_allowed_mime_types() );



if ( ! empty( $field['value'] ) ) : ?>
	<div class="listeo-uploaded-file">

		<?php
		if ( is_numeric( $value ) ) {
			$image_src = wp_get_attachment_url( absint( $value ) );
			$filetype = wp_check_filetype( $image_src );
			$extension = $filetype['ext'];
		} else {
			$image_src = $value;
			$extension = ! empty( $extension ) ? $extension : substr( strrchr( $image_src, '.' ), 1 );
		}


		if ( 'image' === wp_ext2type( $extension ) ) : ?>
			<span class="listeo-uploaded-file-preview"><img src="<?php echo esc_url( $image_src ); ?>" />
				<a class="remove-uploaded-file" href="#"><?php _e( 'Remove file', 'listeo_core' ); ?></a></span>
		<?php else : ?>
			<span class="listeo-uploaded-file-name"><?php echo esc_html( basename( $image_src ) ); ?>
				<a class="remove-uploaded-file" href="#"><?php _e( 'Remove file', 'listeo_core' ); ?></a></span>
		<?php endif; ?>

		<input type="hidden" <?php if ( ! empty( $field['required'] ) )
			echo 'required'; ?> class="input-text" name="current_<?php echo esc_attr( $field['name'] ); ?>" value="<?php echo esc_attr( $value ); ?>" />

	</div>

<?php endif; ?>

<script>
	function checkFileType(event) {
		const fileInput = event.target;  // Get the file input element from the event target
		const file = fileInput.files[0];
		const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/avif'];
		const errorMessage = fileInput.parentElement.nextElementSibling;

		// Clear previous error message
		errorMessage.style.display = 'none';

		if (file) {
			if (!allowedTypes.includes(file.type)) {
				errorMessage.style.display = 'block';  // Show error message
				fileInput.value = '';  // Clear the input field
			}
		}
	}
</script>
<!-- Upload Button -->
<div class="uploadButton margin-top-0">

	<input onchange="checkFileType(event)" accept=".jpg, .jpeg, .png, .webp, .avif" <?php if ( empty( $field['value'] ) ) :
		if ( ! empty( $field['required'] ) )
			echo 'required';
	endif; ?> class="uploadButton-input" type="file" name="<?php echo esc_attr( isset( $field['name'] ) ? $field['name'] : $key ); ?>" id="<?php echo esc_attr( $key ); ?>" />

	<label class="uploadButton-button ripple-effect" for="<?php echo esc_attr( $key ); ?>"><?php esc_html_e( 'Upload File', 'listeo_core' ); ?></label>
	<span class="uploadButton-file-name"><?php printf( esc_html__( 'Maximum file size: %s.', 'listeo_core' ), size_format( '5242880' ) ); ?></span>

</div>
<div class="notification error listing-manager-error file-error-msg" style="display:none">Please upload a valid image file (JPG, PNG, WebP, AVIF).</div>