<?php
/**
 * Plugin Name: Listeo Changes
 * Description: Custom Listeo Changes
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Author URI: https://bigtechies.com
 * Version: 0.0.1
 * Text Domain: listeo-changes
 * Requires at least: 5.2
 * Requires PHP: 7.1
 */

if ( is_plugin_active( 'listeo-core/listeo-core.php' ) ) {
	add_filter( 'submit_listing_steps', 'custom_remove_preview_step' );
	function custom_remove_preview_step( $steps ) {
		if ( isset( $steps['type'] ) ) {
			unset( $steps['type'] );
		}

		if ( isset( $steps['preview'] ) ) {
			unset( $steps['preview'] );
		}

		return $steps;
	}

	function custom_add_human_readable_address_fields( $fields ) {
		$fields['fields'][] = array(
			'name' => __( 'Use Human Readable Address', 'listeo_core' ),
			'id' => '_use_friendly_address',
			'type' => 'checkbox',
		);

		return $fields;
	}

	function custom_published_listing_email( $post ) {
		if ( $post->post_type != 'listing' ) {
			return;
		}

		if ( ! get_option( 'listeo_listing_published_email' ) ) {
			return;
		}

		if ( get_post_meta( $post->ID, 'listeo_published_mail_send', true ) == "sent" ) {
			return;
		}

		$emails = Listeo_Core_Emails::instance();

		$author = get_userdata( $post->post_author );
		$email = $author->data->user_email;
		if ( get_the_author_meta( 'email_notifications', $post->post_author ) == 'on' ) {
			return;
		}
		$args = array(
			'user_name' => $author->display_name,
			'user_mail' => $email,
			'listing_date' => $post->post_date,
			'listing_name' => $post->post_title,
			'listing_url' => get_permalink( $post->ID ),
			'first_name' => $author->first_name,
		);

		$subject = get_option( 'listeo_listing_published_email_subject' );
		$subject = $emails->replace_shortcode( $args, $subject );

		$body = get_option( 'listeo_listing_published_email_content' );
		$body = $emails->replace_shortcode( $args, $body );
		update_post_meta( $post->ID, 'listeo_published_mail_send', 'sent' );
		$emails->send( $email, $subject, $body );
	}

	function custom_new_listing_email( $post_id ) {
		$post = get_post( $post_id );
		if ( $post->post_type !== 'listing' ) {
			return;
		}

		if ( ! get_option( 'listeo_listing_new_email' ) ) {
			return;
		}

		$is_send = get_post_meta( $post->ID, 'new_listing_email_notification', true );
		if ( $is_send ) {
			return;
		}

		$emails = Listeo_Core_Emails::instance();

		$author = get_userdata( $post->post_author );
		$email = $author->data->user_email;
		if ( get_the_author_meta( 'email_notifications', $post->post_author ) == 'on' ) {
			return;
		}
		$args = array(
			'user_name' => $author->display_name,
			'user_mail' => $email,
			'listing_date' => $post->post_date,
			'listing_name' => $post->post_title,
			'listing_url' => get_permalink( $post->ID ),
			'first_name' => $author->first_name,
		);

		$subject = get_option( 'listeo_listing_new_email_subject' );
		$subject = $emails->replace_shortcode( $args, $subject );

		$body = get_option( 'listeo_listing_new_email_content' );
		$body = $emails->replace_shortcode( $args, $body );
		update_post_meta( $post->ID, 'new_listing_email_notification', 'sent' );
		$emails->send( $email, $subject, $body );
	}

	function custom_expired_listing_email( $post_id ) {
		$post = get_post( $post_id );
		if ( $post->post_type !== 'listing' ) {
			return;
		}

		if ( ! get_option( 'listeo_listing_expired_email' ) ) {
			return;
		}

		$emails = Listeo_Core_Emails::instance();

		$author = get_userdata( $post->post_author );
		$email = $author->data->user_email;
		if ( get_the_author_meta( 'email_notifications', $post->post_author ) == 'on' ) {
			return;
		}
		$args = array(
			'user_name' => $author->display_name,
			'user_mail' => $email,
			'listing_date' => $post->post_date,
			'listing_name' => $post->post_title,
			'listing_url' => get_permalink( $post->ID ),
			'first_name' => $author->first_name,
		);

		$subject = get_option( 'listeo_listing_expired_email_subject' );
		$subject = $emails->replace_shortcode( $args, $subject );

		$body = get_option( 'listeo_listing_expired_email_content' );
		$body = $emails->replace_shortcode( $args, $body );

		$emails->send( $email, $subject, $body );
	}

	function custom_expiring_soon_listing_email( $post_id ) {
		$post = get_post( $post_id );
		if ( $post->post_type !== 'listing' ) {
			return;
		}
		// check post status
		if ( $post->post_status !== 'publish' ) {
			return;
		}
		$already_sent = get_post_meta( $post_id, 'notification_email_sent', true );
		if ( $already_sent ) {
			return;
		}

		if ( ! get_option( 'listeo_listing_expiring_soon_email' ) ) {
			return;
		}

		$emails = Listeo_Core_Emails::instance();

		$author = get_userdata( $post->post_author );
		$email = $author->data->user_email;
		if ( get_the_author_meta( 'email_notifications', $post->post_author ) == 'on' ) {
			return;
		}
		$args = array(
			'user_name' => $author->display_name,
			'user_mail' => $email,
			'listing_date' => $post->post_date,
			'listing_name' => $post->post_title,
			'listing_url' => get_permalink( $post->ID ),
			'first_name' => $author->first_name,
		);

		$subject = get_option( 'listeo_listing_expiring_soon_email_subject' );
		$subject = $emails->replace_shortcode( $args, $subject );

		$body = get_option( 'listeo_listing_expiring_soon_email_content' );
		$body = $emails->replace_shortcode( $args, $body );
		add_post_meta( $post_id, 'notification_email_sent', true );
		$emails->send( $email, $subject, $body );

	}

	add_action( 'plugins_loaded', 'remove_default_expired_listing_email' );
	function remove_default_expired_listing_email() {
		$emails = Listeo_Core_Emails::instance();

		remove_action( 'pending_to_publish', [ $emails, 'published_listing_email' ] );
		remove_action( 'listeo_core_listing_submitted', [ $emails, 'new_listing_email' ] );
		remove_action( 'listeo_core_expired_listing', [ $emails, 'expired_listing_email' ] );
		remove_action( 'listeo_core_expiring_soon_listing', [ $emails, 'expiring_soon_listing_email' ] );

		add_action( 'pending_to_publish', 'custom_published_listing_email' );
		add_action( 'listeo_core_listing_submitted', 'custom_new_listing_email' );
		add_action( 'listeo_core_expired_listing', 'custom_expired_listing_email' );
		add_action( 'listeo_core_expiring_soon_listing', 'custom_expiring_soon_listing_email' );

		add_filter( 'listeo_location_fields', 'custom_add_human_readable_address_fields' );
	}

}