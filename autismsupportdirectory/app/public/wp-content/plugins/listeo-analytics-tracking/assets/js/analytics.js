// Track link clicks
document.addEventListener('DOMContentLoaded', function () {
  // POST-tracking helper using Fetch API
  function trackLinkClick(linkType) {
    fetch(listeo_analytics.ajax_url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        action: 'track_link_click',
        nonce: listeo_analytics.nonce,
        listing_id: listeo_analytics.listing_id,
        link_type: linkType,
      }),
    })
      .then((response) => {
        if (!response.ok) throw new Error('Network response was not ok');
        return response.text();
      })
      .then((text) => {
        console.log('Link click tracked:', linkType);
      })
      .catch((err) => {
        console.log('Failed to track link click:', linkType, err);
      });
  }

  document.querySelectorAll('.elementor-widget-listeo-listing-socials a').forEach(function (a) {
    a.addEventListener('click', function (e) {
      trackLinkClick(a.getAttribute('data-type'));
    });
  });
});
