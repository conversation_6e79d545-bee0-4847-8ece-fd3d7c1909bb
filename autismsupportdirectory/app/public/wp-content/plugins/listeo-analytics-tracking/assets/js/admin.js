jQuery(document).ready(function ($) {
  var dailyChart, clicksChart;
  var isLoading = false;
  var currentPage = 1;
  var perPage = 10;
  var totalPages = 1;

  // Initialize charts with better performance settings
  function initCharts() {
    // Daily activity chart
    var dailyCtx = document.getElementById('daily-chart').getContext('2d');
    dailyChart = new Chart(dailyCtx, {
      type: 'line',
      data: {
        labels: [],
        datasets: [
          {
            label: 'Views',
            data: [],
            borderColor: '#0073aa',
            backgroundColor: 'rgba(0, 115, 170, 0.1)',
            tension: 0.4,
            pointRadius: 3,
            pointHoverRadius: 5,
          },
          {
            label: 'Link Clicks',
            data: [],
            borderColor: '#46b450',
            backgroundColor: 'rgba(70, 180, 80, 0.1)',
            tension: 0.4,
            pointRadius: 3,
            pointHoverRadius: 5,
          },
          {
            label: 'Enquiries',
            data: [],
            borderColor: '#ffb900',
            backgroundColor: 'rgba(255, 185, 0, 0.1)',
            tension: 0.4,
            pointRadius: 3,
            pointHoverRadius: 5,
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        animation: {
          duration: 750,
          easing: 'easeInOutQuart',
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              stepSize: 1,
            },
          },
        },
        plugins: {
          legend: {
            position: 'top',
          },
        },
        interaction: {
          intersect: false,
          mode: 'index',
        },
      },
    });

    // Link clicks breakdown chart
    var clicksCtx = document.getElementById('clicks-chart').getContext('2d');
    clicksChart = new Chart(clicksCtx, {
      type: 'doughnut',
      data: {
        labels: [],
        datasets: [
          {
            data: [],
            backgroundColor: ['#0073aa', '#46b450', '#ffb900', '#dc3232', '#826eb4', '#00a0d2', '#d54e21', '#7ad03a', '#f7a738', '#e14d43'],
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        animation: {
          duration: 750,
          easing: 'easeInOutQuart',
        },
        plugins: {
          legend: {
            position: 'right',
            labels: {
              padding: 10,
              usePointStyle: true,
            },
          },
        },
      },
    });
  }

  // Debounced load function to prevent multiple rapid requests
  var loadTimeout;
  function debouncedLoadAnalyticsData(page) {
    clearTimeout(loadTimeout);
    loadTimeout = setTimeout(function () {
      loadAnalyticsData(page);
    }, 300);
  }

  // Load analytics data with loading states
  function loadAnalyticsData(page) {
    if (isLoading) return;
    isLoading = true;
    var days = $('#date-range').val();
    if (typeof page === 'number') currentPage = page;

    // Show loading state
    $('#loading-indicator').show();
    $('#update-charts').prop('disabled', true).text('Loading...');

    $.ajax({
      url: listeo_analytics_admin.ajax_url,
      type: 'POST',
      data: {
        action: 'get_analytics_data',
        nonce: listeo_analytics_admin.nonce,
        days: days,
        page: currentPage,
        per_page: perPage,
      },
      timeout: 30000, // 30 second timeout
      success: function (response) {
        if (response.success) {
          updateDashboard(response.data);
        } else {
          showError('Failed to load data: ' + (response.data || 'Unknown error'));
        }
      },
      error: function (xhr, status, error) {
        console.log('Analytics load error:', error);
        if (status === 'timeout') {
          showError('Request timed out. Please try again.');
        } else {
          showError('Failed to load analytics data. Please refresh the page.');
        }
      },
      complete: function () {
        isLoading = false;
        $('#loading-indicator').hide();
        $('#update-charts').prop('disabled', false).text('Update Charts');
      },
    });
  }

  // Show error message
  function showError(message) {
    var errorHtml = '<div class="notice notice-error is-dismissible"><p>' + message + '</p></div>';
    $('.wrap h1').after(errorHtml);

    // Auto-dismiss after 5 seconds
    setTimeout(function () {
      $('.notice-error').fadeOut();
    }, 5000);
  }

  // Update dashboard with data
  function updateDashboard(data) {
    // Update summary numbers
    var totals = {};
    data.totals.forEach(function (item) {
      totals[item.event_type] = parseInt(item.total);
    });

    $('#total-views').text(totals.view || 0);
    $('#total-clicks').text(totals.click || 0);
    $('#total-enquiries').text(totals.enquiry || 0);

    // Update charts with animation
    updateDailyChart(data.daily_data);
    updateClicksChart(data.click_breakdown);

    // Update top listings table with pagination
    updateTopListingsTable(data.top_listings, data.link_types, data.page, data.per_page, data.top_listings_total);
  }

  // Update daily activity chart
  function updateDailyChart(dailyData) {
    var dates = [];
    var views = [];
    var clicks = [];
    var enquiries = [];

    // Group data by date
    var groupedData = {};
    dailyData.forEach(function (item) {
      if (!groupedData[item.date]) {
        groupedData[item.date] = { view: 0, click: 0, enquiry: 0 };
      }
      groupedData[item.date][item.event_type] = parseInt(item.total);
    });

    // Sort dates and create arrays
    Object.keys(groupedData)
      .sort()
      .forEach(function (date) {
        dates.push(formatDate(date));
        views.push(groupedData[date].view);
        clicks.push(groupedData[date].click);
        enquiries.push(groupedData[date].enquiry);
      });

    // Update chart with animation
    dailyChart.data.labels = dates;
    dailyChart.data.datasets[0].data = views;
    dailyChart.data.datasets[1].data = clicks;
    dailyChart.data.datasets[2].data = enquiries;
    dailyChart.update('active');
  }

  // Update clicks breakdown chart
  function updateClicksChart(clickBreakdown) {
    var labels = [];
    var data = [];

    clickBreakdown.forEach(function (item) {
      labels.push(formatLinkType(item.event_subtype));
      data.push(parseInt(item.total));
    });

    clicksChart.data.labels = labels;
    clicksChart.data.datasets[0].data = data;
    clicksChart.update('active');
  }

  // Update top listings table with pagination
  function updateTopListingsTable(topListings, linkTypes, page, perPage, total) {
    var tbody = $('#top-listings');
    var thead = $('#top-listings').closest('table').find('thead tr');
    tbody.empty();

    // Update table header
    var headerHtml = '<th>Listing</th><th>Views</th>';
    linkTypes.forEach(function (type) {
      headerHtml += '<th>' + formatLinkType(type) + '</th>';
    });
    headerHtml += '<th>Enquiries</th>';
    thead.html(headerHtml);

    if (topListings.length === 0) {
      tbody.append('<tr><td colspan="' + (3 + linkTypes.length) + '">No data available</td></tr>');
      $('#top-listings-pagination').remove();
      return;
    }

    topListings.forEach(function (listing) {
      var row = '<tr>' + '<td><a href="' + listing.url + '" target="_blank">' + listing.title + '</a></td>' + '<td>' + listing.views + '</td>';
      linkTypes.forEach(function (type) {
        row += '<td>' + (listing[type] || 0) + '</td>';
      });
      row += '<td>' + listing.enquiries + '</td>';
      row += '</tr>';
      tbody.append(row);
    });

    // Pagination controls
    var totalPages = Math.ceil(total / perPage);
    var paginationHtml = '<div id="top-listings-pagination" style="margin-top:10px;text-align:right;">';
    if (totalPages > 1) {
      if (page > 1) {
        paginationHtml += '<button class="top-listings-page-btn" data-page="' + (page - 1) + '">&laquo; Prev</button> ';
      }
      for (var i = 1; i <= totalPages; i++) {
        if (i === page) {
          paginationHtml += '<button class="top-listings-page-btn active" disabled>' + i + '</button> ';
        } else if (i === 1 || i === totalPages || (i >= page - 2 && i <= page + 2)) {
          paginationHtml += '<button class="top-listings-page-btn" data-page="' + i + '">' + i + '</button> ';
        } else if (i === page - 3 || i === page + 3) {
          paginationHtml += '... ';
        }
      }
      if (page < totalPages) {
        paginationHtml += '<button class="top-listings-page-btn" data-page="' + (page + 1) + '">Next &raquo;</button>';
      }
    }
    paginationHtml += '</div>';
    // Remove old and add new
    $('#top-listings-pagination').remove();
    $(tbody).closest('.analytics-table').append(paginationHtml);
  }

  // Pagination click handler
  $(document).on('click', '.top-listings-page-btn', function () {
    var page = parseInt($(this).data('page'));
    if (!isNaN(page) && page !== currentPage) {
      loadAnalyticsData(page);
    }
  });

  // Helper functions
  function formatDate(dateString) {
    var date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  }

  function formatLinkType(linkType) {
    linkType = linkType.charAt(0).toUpperCase() + linkType.slice(1);

    return linkType;
  }

  // Event handlers with debouncing
  $('#update-charts').on('click', function () {
    debouncedLoadAnalyticsData(currentPage);
  });

  $('#date-range').on('change', function () {
    debouncedLoadAnalyticsData(1); // Reset to page 1 on date change
  });

  // Initialize with a slight delay to ensure DOM is ready
  setTimeout(function () {
    initCharts();
    loadAnalyticsData(1);
  }, 100);
});
