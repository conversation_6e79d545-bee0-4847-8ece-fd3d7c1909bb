<?php

/**
 * Plugin Name: Listeo Analytics Tracking
 * Description: Track listing views, link clicks, and enquiry form submissions with daily analytics
 * Version: 1.0.0
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Author URI: https://bigtechies.com
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

class Listeo_Analytics_Tracking {

	public function __construct() {
        // * This is already tracked in listeo-child functions.php track_listing_click function
		// add_action( 'wp_enqueue_scripts', array( $this, 'enqueue_scripts' ) );
		// add_action( 'wp_ajax_track_link_click', array( $this, 'track_link_click' ) );
		// add_action( 'wp_ajax_nopriv_track_link_click', array( $this, 'track_link_click' ) );
		add_action( 'wpcf7_mail_sent', array( $this, 'track_enquiry_submission' ) );
		add_action( 'wp_head', array( $this, 'enhance_view_tracking' ) );
		add_action( 'admin_menu', array( $this, 'add_admin_menu' ) );
		add_action( 'admin_enqueue_scripts', array( $this, 'admin_scripts' ) );
		add_action( 'wp_ajax_get_analytics_data', array( $this, 'get_analytics_data' ) );

		// Create database tables on plugin activation
		register_activation_hook( __FILE__, array( $this, 'create_tables' ) );
	}

	public function create_tables() {
		global $wpdb;

		$charset_collate = $wpdb->get_charset_collate();

		// Table for daily analytics
		$table_name = $wpdb->prefix . 'listeo_analytics_daily';
		$sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            date date NOT NULL,
            event_type varchar(50) NOT NULL,
            event_subtype varchar(100) DEFAULT '',
            listing_id bigint(20) DEFAULT 0,
            count int(11) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY date_event (date, event_type, event_subtype, listing_id),
            KEY idx_date_event (date, event_type),
            KEY idx_listing_date (listing_id, date),
            KEY idx_event_subtype (event_type, event_subtype)
        ) $charset_collate;";

		require_once( ABSPATH . 'wp-admin/includes/upgrade.php' );
		dbDelta( $sql );
	}

	public function enqueue_scripts() {
		if ( is_singular( 'listing' ) ) {
			wp_enqueue_script( 'listeo-analytics', plugin_dir_url( __FILE__ ) . 'assets/js/analytics.js', [], '1.0.1' );
			wp_localize_script( 'listeo-analytics', 'listeo_analytics', array(
				'ajax_url' => admin_url( 'admin-ajax.php' ),
				'nonce' => wp_create_nonce( 'listeo_analytics_nonce' ),
				'listing_id' => get_the_ID()
			) );
		}
	}

	public function admin_scripts( $hook ) {
		if ( 'toplevel_page_listeo-analytics' !== $hook ) {
			return;
		}

		wp_enqueue_script( 'chart-js', 'https://cdn.jsdelivr.net/npm/chart.js', array(), '3.9.1', true );
		wp_enqueue_script( 'listeo-analytics-admin', plugin_dir_url( __FILE__ ) . 'assets/js/admin.js', array( 'jquery', 'chart-js' ), '1.0.3', true );
		wp_localize_script( 'listeo-analytics-admin', 'listeo_analytics_admin', array(
			'ajax_url' => admin_url( 'admin-ajax.php' ),
			'nonce' => wp_create_nonce( 'listeo_analytics_admin_nonce' )
		) );
	}

	public function enhance_view_tracking() {
		if ( is_singular( 'listing' ) ) {
			// Track the view in our analytics system
			$this->track_event( 'view', 'listing', get_the_ID() );
		}
	}

    // * This is already tracked in listeo-child functions.php track_listing_click function
	// public function track_link_click() {
	// 	check_ajax_referer( 'listeo_analytics_nonce', 'nonce' );

	// 	$listing_id = intval( $_POST['listing_id'] );
	// 	$link_type = sanitize_text_field( $_POST['link_type'] );

	// 	$allowed_types = [ 'call', 'email', 'website', 'facebook', 'instagram' ];

	// 	if ( $listing_id > 0 && in_array( $link_type, $allowed_types ) ) {
	// 		$this->track_event( 'click', $link_type, $listing_id );
	// 		wp_send_json_success();
	// 	} else {
	// 		error_log( "track_link_click: Invalid input" );
	// 		wp_send_json_error( [ 'message' => 'Invalid input data.' ] );
	// 	}
	// }

	public function track_enquiry_submission() {
		// Get the listing ID from the form submission
		$listing_id = 0;

		// Try to get listing ID from form data
		$submission = WPCF7_Submission::get_instance();
		if ( $submission ) {
			$data = $submission->get_posted_data();
			// Check if this is a listing contact form
			if ( isset( $data['listing-id'] ) && ! empty( $data['listing-id'] ) ) {
				$listing_id = intval( $data['listing-id'] );
			}
		}

		if ( $listing_id ) {
			$this->track_event( 'enquiry', 'contact_form', $listing_id );
		}
	}

	public function track_event( $event_type, $event_subtype, $listing_id = 0 ) {
		global $wpdb;

		$date = current_time( 'Y-m-d' );

		// Update daily summary
		$table_name = $wpdb->prefix . 'listeo_analytics_daily';
		$result = $wpdb->get_row( $wpdb->prepare(
			"SELECT * FROM $table_name WHERE date = %s AND event_type = %s AND event_subtype = %s AND listing_id = %d",
			$date,
			$event_type,
			$event_subtype,
			$listing_id
		) );

		if ( $result ) {
			// Update existing record
			$wpdb->update(
				$table_name,
				array( 'count' => $result->count + 1 ),
				array(
					'date' => $date,
					'event_type' => $event_type,
					'event_subtype' => $event_subtype,
					'listing_id' => $listing_id
				),
				array( '%d' ),
				array( '%s', '%s', '%s', '%d' )
			);
		} else {
			// Insert new record
			$wpdb->insert(
				$table_name,
				array(
					'date' => $date,
					'event_type' => $event_type,
					'event_subtype' => $event_subtype,
					'listing_id' => $listing_id,
					'count' => 1
				),
				array( '%s', '%s', '%s', '%d', '%d' )
			);
		}

		// Fire action for other plugins to hook into
		do_action( 'listeo_analytics_event_tracked', $event_type, $event_subtype, $listing_id, $date );
	}

	/**
	 * Public method to track events from other plugins/themes
	 */
	public function track_custom_event( $event_type, $event_subtype, $listing_id = 0 ) {
		$this->track_event( $event_type, $event_subtype, $listing_id );
	}

	public function add_admin_menu() {
		add_menu_page(
			'Listeo Analytics',
			'Listeo Analytics',
			'manage_options',
			'listeo-analytics',
			array( $this, 'admin_page' ),
			'dashicons-chart-area',
			30
		);
	}

	public function admin_page() {
		?>
		<div class="wrap">
			<h1>Listeo Analytics Dashboard</h1>

			<div class="analytics-filters">
				<select id="date-range">
					<option value="1">Today</option>
					<option value="7">Last 7 days</option>
					<option value="30" selected>Last 30 days</option>
					<option value="90">Last 90 days</option>
					<option value="365">Last year</option>
				</select>
				<button id="update-charts" class="button button-primary">Update Charts</button>
				<span id="loading-indicator" style="display:none; margin-left: 10px;">Loading...</span>
			</div>

			<div class="analytics-summary">
				<div class="analytics-card">
					<h3>Total Views</h3>
					<div id="total-views" class="analytics-number">-</div>
				</div>
				<div class="analytics-card">
					<h3>Total Link Clicks</h3>
					<div id="total-clicks" class="analytics-number">-</div>
				</div>
				<div class="analytics-card">
					<h3>Total Enquiries</h3>
					<div id="total-enquiries" class="analytics-number">-</div>
				</div>
			</div>

			<div class="analytics-charts">
				<div class="chart-container">
					<h3>Activity</h3>
					<div style="height: 300px;">
						<canvas id="daily-chart"></canvas>
					</div>
				</div>
				<div class="chart-container">
					<h3>Link Click Breakdown</h3>
					<div style="height: 300px;">
						<canvas id="clicks-chart"></canvas>
					</div>
				</div>
			</div>

			<div class="analytics-table">
				<h3>Top Performing Listings</h3>
				<table class="wp-list-table widefat fixed striped">
					<thead>
						<tr>
							<th>Listing</th>
						</tr>
					</thead>
					<tbody id="top-listings">
						<tr>
							<td colspan="5">Loading...</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>

		<style>
			.analytics-filters {
				margin: 20px 0;
				padding: 15px;
				background: #fff;
				border: 1px solid #ddd;
				border-radius: 4px;
			}

			.analytics-summary {
				display: grid;
				grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
				gap: 20px;
				margin: 20px 0;
			}

			.analytics-card {
				background: #fff;
				padding: 20px;
				border: 1px solid #ddd;
				border-radius: 4px;
				text-align: center;
			}

			.analytics-number {
				font-size: 2em;
				font-weight: bold;
				color: #0073aa;
			}

			.analytics-charts {
				display: grid;
				grid-template-columns: 1fr 1fr;
				gap: 20px;
				margin: 20px 0;
			}

			.chart-container {
				background: #fff;
				padding: 20px;
				border: 1px solid #ddd;
				border-radius: 4px;
			}

			.analytics-table {
				background: #fff;
				padding: 20px;
				border: 1px solid #ddd;
				border-radius: 4px;
				margin: 20px 0;
			}

			@media (max-width: 768px) {
				.analytics-charts {
					grid-template-columns: 1fr;
				}
			}
		</style>
		<?php
	}

	public function get_analytics_data() {
		check_ajax_referer( 'listeo_analytics_admin_nonce', 'nonce' );

		$days = intval( $_POST['days'] ?? 30 );
		$start_date = date( 'Y-m-d', strtotime( "-$days days" ) );
		$end_date = date( 'Y-m-d' );

		$page = isset( $_POST['page'] ) ? max( 1, intval( $_POST['page'] ) ) : 1;
		$per_page = isset( $_POST['per_page'] ) ? max( 1, intval( $_POST['per_page'] ) ) : 10;
		$offset = ( $page - 1 ) * $per_page;

		global $wpdb;

		// Get daily data (unchanged)
		$daily_data = $wpdb->get_results( $wpdb->prepare(
			"SELECT date, event_type, SUM(count) as total
             FROM {$wpdb->prefix}listeo_analytics_daily
             WHERE date BETWEEN %s AND %s
             GROUP BY date, event_type
             ORDER BY date ASC",
			$start_date,
			$end_date
		) );

		// Get summary totals (unchanged)
		$totals = $wpdb->get_results( $wpdb->prepare(
			"SELECT event_type, SUM(count) as total
             FROM {$wpdb->prefix}listeo_analytics_daily
             WHERE date BETWEEN %s AND %s
             GROUP BY event_type",
			$start_date,
			$end_date
		) );

		// Get all link types used in the date range (for columns)
		$link_types = $wpdb->get_col( $wpdb->prepare(
			"SELECT DISTINCT event_subtype FROM {$wpdb->prefix}listeo_analytics_daily
             WHERE date BETWEEN %s AND %s AND event_type = 'click' AND event_subtype != ''
             ORDER BY event_subtype ASC",
			$start_date,
			$end_date
		) );
		if ( count( $link_types ) > 8 ) {
			$link_types = array_slice( $link_types, 0, 8 );
		}

		// Get total count of listings for pagination
		$top_listings_total = $wpdb->get_var( $wpdb->prepare(
			"SELECT COUNT(*) FROM (
                SELECT listing_id,
                       SUM(CASE WHEN event_type = 'view' THEN count ELSE 0 END) AS views,
                       SUM(CASE WHEN event_type = 'enquiry' THEN count ELSE 0 END) AS enquiries,
                       SUM(CASE WHEN event_type = 'click' THEN count ELSE 0 END) AS clicks
                FROM {$wpdb->prefix}listeo_analytics_daily
                WHERE date BETWEEN %s AND %s AND listing_id > 0
                GROUP BY listing_id
            ) AS stats
            WHERE (views + enquiries + clicks) > 0",
			$start_date,
			$end_date
		) );

		// Get top listings (paginated)
		$top_listings = $wpdb->get_results( $wpdb->prepare(
			"SELECT
                listing_id,
                views,
                enquiries,
                clicks
            FROM (
                SELECT listing_id,
                    SUM(CASE WHEN event_type = 'view' THEN count ELSE 0 END) as views,
                    SUM(CASE WHEN event_type = 'enquiry' THEN count ELSE 0 END) as enquiries,
                    SUM(CASE WHEN event_type = 'click' THEN count ELSE 0 END) as clicks
                FROM {$wpdb->prefix}listeo_analytics_daily
                WHERE date BETWEEN %s AND %s AND listing_id > 0
                GROUP BY listing_id
            ) AS stats
            WHERE (views + enquiries + clicks) > 0
            ORDER BY (views + enquiries + clicks) DESC
            LIMIT %d OFFSET %d",
			$start_date,
			$end_date,
			$per_page,
			$offset
		) );

		// For each top listing, get click counts for each link type
		foreach ( $top_listings as $listing ) {
			$listing_id = $listing->listing_id;
			// Get click counts for this listing by link type
			$click_counts = $wpdb->get_results( $wpdb->prepare(
				"SELECT event_subtype, SUM(count) as total
                 FROM {$wpdb->prefix}listeo_analytics_daily
                 WHERE date BETWEEN %s AND %s AND event_type = 'click' AND listing_id = %d
                 GROUP BY event_subtype",
				$start_date,
				$end_date,
				$listing_id
			) );
			$clicks_by_type = array_fill_keys( $link_types, 0 );
			foreach ( $click_counts as $row ) {
				if ( in_array( $row->event_subtype, $link_types ) ) {
					$clicks_by_type[ $row->event_subtype ] = intval( $row->total );
				}
			}
			// Attach click counts for each type
			foreach ( $link_types as $type ) {
				$listing->{$type} = $clicks_by_type[ $type ];
			}
		}

		// Batch fetch listing titles and URLs to avoid N+1 queries
		if ( ! empty( $top_listings ) ) {
			$listing_ids = wp_list_pluck( $top_listings, 'listing_id' );
			$posts = get_posts( array(
				'post_type' => 'listing',
				'post__in' => $listing_ids,
				'posts_per_page' => -1,
				'post_status' => 'any'
			) );
			$posts_by_id = array();
			foreach ( $posts as $post ) {
				$posts_by_id[ $post->ID ] = $post;
			}
			foreach ( $top_listings as $listing ) {
				if ( isset( $posts_by_id[ $listing->listing_id ] ) ) {
					$post = $posts_by_id[ $listing->listing_id ];
					$listing->title = $post->post_title;
					$listing->url = get_permalink( $post->ID );
				} else {
					$listing->title = 'Listing #' . $listing->listing_id;
					$listing->url = '#';
				}
			}
		}

		// Get link click breakdown (unchanged)
		$click_breakdown = $wpdb->get_results( $wpdb->prepare(
			"SELECT event_subtype, SUM(count) as total
             FROM {$wpdb->prefix}listeo_analytics_daily
             WHERE date BETWEEN %s AND %s AND event_type = 'click'
             GROUP BY event_subtype
             ORDER BY total DESC
             LIMIT 15",
			$start_date,
			$end_date
		) );

		$result_data = array(
			'daily_data' => $daily_data,
			'totals' => $totals,
			'top_listings' => $top_listings,
			'link_types' => $link_types,
			'click_breakdown' => $click_breakdown,
			'top_listings_total' => intval( $top_listings_total ),
			'page' => $page,
			'per_page' => $per_page,
		);

		wp_send_json_success( $result_data );
	}
}

// Initialize the plugin
global $listeo_analytics_tracking;
$listeo_analytics_tracking = new Listeo_Analytics_Tracking();

add_filter( 'shortcode_atts_wpcf7', 'custom_shortcode_atts_wpcf7_filter', 10, 3 );
function custom_shortcode_atts_wpcf7_filter( $out, $pairs, $atts ) {
	if ( is_singular( 'listing' ) ) {
		$hidden_atts = [ 'listing-id' => get_the_ID() ];

		$out = array_merge( $out, $hidden_atts );
	}

	return $out;
}
